../../../bin/__pycache__/fixup_firestore_admin_v1_keywords.cpython-38.pyc,,
../../../bin/__pycache__/fixup_firestore_v1_keywords.cpython-38.pyc,,
../../../bin/fixup_firestore_admin_v1_keywords.py,sha256=erZEJZPpDWiL4704QeaxqTmpD0mIUpIcnLV8f7SYHHg,6822
../../../bin/fixup_firestore_v1_keywords.py,sha256=7vR_cjDxcwxabSi_ngEfuGRIJSRZhsWf5vv_D6pr2Do,7483
google/cloud/firestore/__init__.py,sha256=Xh7wydIH6QtvJqe-nAWgL5T_S5qhCiXdiOt1MxwDR9k,3338
google/cloud/firestore/__pycache__/__init__.cpython-38.pyc,,
google/cloud/firestore/__pycache__/gapic_version.cpython-38.pyc,,
google/cloud/firestore/gapic_version.py,sha256=Q9GANDHaTnQvUDT7UUk1w_43IaPn8q7PucdTFHDod7o,653
google/cloud/firestore_admin_v1/__init__.py,sha256=uL69T_lG86f7NYMy0amW20r8_t0iI9zR-KQfRbAUp3Y,2282
google/cloud/firestore_admin_v1/__pycache__/__init__.cpython-38.pyc,,
google/cloud/firestore_admin_v1/__pycache__/gapic_version.cpython-38.pyc,,
google/cloud/firestore_admin_v1/gapic_metadata.json,sha256=rsV_RSyN_UhUU5fSBceq-1EVQaZXag6JcmQyQ_ce_Ws,5322
google/cloud/firestore_admin_v1/gapic_version.py,sha256=Q9GANDHaTnQvUDT7UUk1w_43IaPn8q7PucdTFHDod7o,653
google/cloud/firestore_admin_v1/py.typed,sha256=986zLtpMXwL-7nXabn6tbnx8AMKbxkX0e3KeCdrySvA,89
google/cloud/firestore_admin_v1/services/__init__.py,sha256=zWbvQwgy48VXnYBlD4x4jlBof70BFYtIC9fehwEW1WQ,600
google/cloud/firestore_admin_v1/services/__pycache__/__init__.cpython-38.pyc,,
google/cloud/firestore_admin_v1/services/firestore_admin/__init__.py,sha256=d2InWZmHCpBSQnSRYaisE0WjeRAOjh18X1gSUK055uU,769
google/cloud/firestore_admin_v1/services/firestore_admin/__pycache__/__init__.cpython-38.pyc,,
google/cloud/firestore_admin_v1/services/firestore_admin/__pycache__/async_client.cpython-38.pyc,,
google/cloud/firestore_admin_v1/services/firestore_admin/__pycache__/client.cpython-38.pyc,,
google/cloud/firestore_admin_v1/services/firestore_admin/__pycache__/pagers.cpython-38.pyc,,
google/cloud/firestore_admin_v1/services/firestore_admin/async_client.py,sha256=XVrfyJ8eDn4L3bNt-PGQJDkWJ2uuCSjh8mQVWZMK-9s,85361
google/cloud/firestore_admin_v1/services/firestore_admin/client.py,sha256=DvBTORPDRf_nZed8A3h6OcFWjKYA26eApRpZyUw5jZg,95896
google/cloud/firestore_admin_v1/services/firestore_admin/pagers.py,sha256=7Tj4UhWMLdoJH01ngrzcsvo6A-Tu9V6GVKFan9qydgg,10866
google/cloud/firestore_admin_v1/services/firestore_admin/transports/__init__.py,sha256=YilajM9zZrwwf_FeU6lVxqsLariQiSuc7HmsFJvHxxA,1418
google/cloud/firestore_admin_v1/services/firestore_admin/transports/__pycache__/__init__.cpython-38.pyc,,
google/cloud/firestore_admin_v1/services/firestore_admin/transports/__pycache__/base.cpython-38.pyc,,
google/cloud/firestore_admin_v1/services/firestore_admin/transports/__pycache__/grpc.cpython-38.pyc,,
google/cloud/firestore_admin_v1/services/firestore_admin/transports/__pycache__/grpc_asyncio.cpython-38.pyc,,
google/cloud/firestore_admin_v1/services/firestore_admin/transports/__pycache__/rest.cpython-38.pyc,,
google/cloud/firestore_admin_v1/services/firestore_admin/transports/base.py,sha256=antFXfb7pqqJcIWmv6j_Tc_-RA8HdVsz_SGsjRF9JxE,15023
google/cloud/firestore_admin_v1/services/firestore_admin/transports/grpc.py,sha256=d2ZmDHcGatu_ksHkihHYfC41O0wlOqmjqiQUn5GlyLg,33083
google/cloud/firestore_admin_v1/services/firestore_admin/transports/grpc_asyncio.py,sha256=dLRUllUUDnxli4cA50TEIienGutVoWuzfOpZs6Ihz6k,33738
google/cloud/firestore_admin_v1/services/firestore_admin/transports/rest.py,sha256=-Yrkd2MHF7wWSXx82GFxsR6-Vp5IvzfXHU-R1tLDVcI,91009
google/cloud/firestore_admin_v1/types/__init__.py,sha256=aG4uUc4QAr153SeodaoNjiGkncwa3LzxNQErjvg3N_Q,2207
google/cloud/firestore_admin_v1/types/__pycache__/__init__.cpython-38.pyc,,
google/cloud/firestore_admin_v1/types/__pycache__/database.cpython-38.pyc,,
google/cloud/firestore_admin_v1/types/__pycache__/field.cpython-38.pyc,,
google/cloud/firestore_admin_v1/types/__pycache__/firestore_admin.cpython-38.pyc,,
google/cloud/firestore_admin_v1/types/__pycache__/index.cpython-38.pyc,,
google/cloud/firestore_admin_v1/types/__pycache__/location.cpython-38.pyc,,
google/cloud/firestore_admin_v1/types/__pycache__/operation.cpython-38.pyc,,
google/cloud/firestore_admin_v1/types/database.py,sha256=G17IVQdA-bTDYpPEPJ6lwkIcktwxBkxH-0KiqOda228,6065
google/cloud/firestore_admin_v1/types/field.py,sha256=pd7gvH1HsC9DjN7WKbgbWRt2tm33spGBqKWGrY1j7mE,7554
google/cloud/firestore_admin_v1/types/firestore_admin.py,sha256=o3SHhCGQt1RniwNAPlAoZL7JDrmPUleP6PsyzUBvJnU,13925
google/cloud/firestore_admin_v1/types/index.py,sha256=oBfD0-PPlnamqdkg_XuRvdCBDBy-HQy2ENNFjo5J_0Y,9693
google/cloud/firestore_admin_v1/types/location.py,sha256=EADOxxwqpbVWmUq8JbqbrQwmhjytz8Sxco756O1oE6g,1061
google/cloud/firestore_admin_v1/types/operation.py,sha256=GWoBWlzUn2yITR5pCR8Ir7ucGrHFmYQgr8fRlMD045w,14189
google/cloud/firestore_bundle/__init__.py,sha256=7mza_CpDC-bac2R68ALozV1WwnbnHX1eoX5oHKdlYcg,1114
google/cloud/firestore_bundle/__pycache__/__init__.cpython-38.pyc,,
google/cloud/firestore_bundle/__pycache__/_helpers.cpython-38.pyc,,
google/cloud/firestore_bundle/__pycache__/bundle.cpython-38.pyc,,
google/cloud/firestore_bundle/__pycache__/gapic_version.cpython-38.pyc,,
google/cloud/firestore_bundle/_helpers.py,sha256=rs3YY_LgD6RRKR3ay2p9gM_1uwBk6hxEY032f_hSNXg,361
google/cloud/firestore_bundle/bundle.py,sha256=jkEFHhwzyuTghMBdda8X_uNZaK_1kiS8ddR0U5feLSk,13930
google/cloud/firestore_bundle/gapic_metadata.json,sha256=WP2fXupExiCP8Mz5jm4QoaOURqGtnA6tv5CGx_cq6A8,231
google/cloud/firestore_bundle/gapic_version.py,sha256=Q9GANDHaTnQvUDT7UUk1w_43IaPn8q7PucdTFHDod7o,653
google/cloud/firestore_bundle/py.typed,sha256=22rn8vq8SChWrPBibDFfX0tiViQW0mXXsTsih71_Ye8,80
google/cloud/firestore_bundle/services/__init__.py,sha256=zWbvQwgy48VXnYBlD4x4jlBof70BFYtIC9fehwEW1WQ,600
google/cloud/firestore_bundle/services/__pycache__/__init__.cpython-38.pyc,,
google/cloud/firestore_bundle/types/__init__.py,sha256=otM_uBZPmrcCq8KIb5EuMrO0dtoUUUJr0VcsVOjU3-g,853
google/cloud/firestore_bundle/types/__pycache__/__init__.cpython-38.pyc,,
google/cloud/firestore_bundle/types/__pycache__/bundle.cpython-38.pyc,,
google/cloud/firestore_bundle/types/bundle.py,sha256=WWKqn9gaKcS4X1DV23Ux3o6HwZn3LvhPAbXr97NW-6I,7500
google/cloud/firestore_v1/__init__.py,sha256=hFNDp0-iGGYezZnDTn3o2Xo9z-qPXCZzPMp5ylBM8SI,5909
google/cloud/firestore_v1/__pycache__/__init__.cpython-38.pyc,,
google/cloud/firestore_v1/__pycache__/_helpers.cpython-38.pyc,,
google/cloud/firestore_v1/__pycache__/aggregation.cpython-38.pyc,,
google/cloud/firestore_v1/__pycache__/async_aggregation.cpython-38.pyc,,
google/cloud/firestore_v1/__pycache__/async_batch.cpython-38.pyc,,
google/cloud/firestore_v1/__pycache__/async_client.cpython-38.pyc,,
google/cloud/firestore_v1/__pycache__/async_collection.cpython-38.pyc,,
google/cloud/firestore_v1/__pycache__/async_document.cpython-38.pyc,,
google/cloud/firestore_v1/__pycache__/async_query.cpython-38.pyc,,
google/cloud/firestore_v1/__pycache__/async_transaction.cpython-38.pyc,,
google/cloud/firestore_v1/__pycache__/base_aggregation.cpython-38.pyc,,
google/cloud/firestore_v1/__pycache__/base_batch.cpython-38.pyc,,
google/cloud/firestore_v1/__pycache__/base_client.cpython-38.pyc,,
google/cloud/firestore_v1/__pycache__/base_collection.cpython-38.pyc,,
google/cloud/firestore_v1/__pycache__/base_document.cpython-38.pyc,,
google/cloud/firestore_v1/__pycache__/base_query.cpython-38.pyc,,
google/cloud/firestore_v1/__pycache__/base_transaction.cpython-38.pyc,,
google/cloud/firestore_v1/__pycache__/batch.cpython-38.pyc,,
google/cloud/firestore_v1/__pycache__/bulk_batch.cpython-38.pyc,,
google/cloud/firestore_v1/__pycache__/bulk_writer.cpython-38.pyc,,
google/cloud/firestore_v1/__pycache__/client.cpython-38.pyc,,
google/cloud/firestore_v1/__pycache__/collection.cpython-38.pyc,,
google/cloud/firestore_v1/__pycache__/document.cpython-38.pyc,,
google/cloud/firestore_v1/__pycache__/field_path.cpython-38.pyc,,
google/cloud/firestore_v1/__pycache__/gapic_version.cpython-38.pyc,,
google/cloud/firestore_v1/__pycache__/order.cpython-38.pyc,,
google/cloud/firestore_v1/__pycache__/query.cpython-38.pyc,,
google/cloud/firestore_v1/__pycache__/rate_limiter.cpython-38.pyc,,
google/cloud/firestore_v1/__pycache__/transaction.cpython-38.pyc,,
google/cloud/firestore_v1/__pycache__/transforms.cpython-38.pyc,,
google/cloud/firestore_v1/__pycache__/watch.cpython-38.pyc,,
google/cloud/firestore_v1/_helpers.py,sha256=gxSPT-UjMa0O2GoJaIbapmToRNrqHfpwuueDSAbAcmw,45859
google/cloud/firestore_v1/aggregation.py,sha256=rCxzQiplucZwdABueQBqUboGD2jFzoyY8MD4yKJRyYQ,5820
google/cloud/firestore_v1/async_aggregation.py,sha256=MYsb0cgSr0CIYUfIrTS5MNwBRoXl1zr0pT5qD4F4QIQ,4702
google/cloud/firestore_v1/async_batch.py,sha256=vp0MKdk2yPA4MRJ63W_Mg9BnUCgtH2ar8PC7pEv57_U,2811
google/cloud/firestore_v1/async_client.py,sha256=rFvtXTxDV7glJlgkpUImEXM9o3W4IPuvUduOn5rxJYs,15485
google/cloud/firestore_v1/async_collection.py,sha256=K3pSXHi03e--IIuqUefZvrG8t3ZJZ4Le1Zsmsw-HsC0,9990
google/cloud/firestore_v1/async_document.py,sha256=84UBBuBgLClEbQdLvWqgkh2gNZLxbOFsNWpT2SlPmp0,16155
google/cloud/firestore_v1/async_query.py,sha256=kGWb-evEocq_AlruCRNOjSr0Vj7Xe0XZt149ieNVagQ,16297
google/cloud/firestore_v1/async_transaction.py,sha256=D9M4DCbWSRm-o90CkHSyeHSYBdEg4MMJlOecEk9CqCc,14742
google/cloud/firestore_v1/base_aggregation.py,sha256=5819o5QjbGEeCDYWudWf5X7aGrFmWM5xxSDnJnyyQJo,9964
google/cloud/firestore_v1/base_batch.py,sha256=KlBVKngVEU5-Dy0KbP6F8P4CabVRNGqDEPrnhx_Fdwg,7216
google/cloud/firestore_v1/base_client.py,sha256=HSm3XccY7Yg-Nxznl5oLpirsM4c3A5O8leJ86WtTpM4,22464
google/cloud/firestore_v1/base_collection.py,sha256=Pj1zggq6_UYmctFh5T8m7REFnrlCeHUGTbV0OOTN31g,20217
google/cloud/firestore_v1/base_document.py,sha256=7b4zzO8cUSAHF2i11nSsHvaT-V54fq8boGV3c3mAiTw,18496
google/cloud/firestore_v1/base_query.py,sha256=2Z5kTwA63ABKbYVZlXPMj5nrc48eqHnuZ_6Kt4_fwLs,56412
google/cloud/firestore_v1/base_transaction.py,sha256=wnnsGQhOjOs9_w6HC_evvMzX1LDeGUe0yKeZcW8yhQA,6468
google/cloud/firestore_v1/batch.py,sha256=u0I3oRWf3Cy09TFzTcGNYFKa1th4dt5deCD1v1WN1TE,2842
google/cloud/firestore_v1/bulk_batch.py,sha256=6ZSpctvtSLA3n-YnmnEWS6uvxg95XSfLJVAZO6dhElc,3870
google/cloud/firestore_v1/bulk_writer.py,sha256=TshquO2f_lhBRpeOd09kpOgDWGBEqwSlds1q4CWzo44,34751
google/cloud/firestore_v1/client.py,sha256=q3KOqlkgpdiTgUjVtubhuBBwOLcLBAeMJjEJO8X84hA,14616
google/cloud/firestore_v1/collection.py,sha256=eB9tHpdHUkTZ65eUMyu1MmTGxpp_iwxAbMHzy1_sD_Y,10296
google/cloud/firestore_v1/document.py,sha256=K6tgFf-_YvTmIUPIb-Qs-B7Nz86LfLg0xxG-OGkBTis,19011
google/cloud/firestore_v1/field_path.py,sha256=EPlGSFdpYhnEYuTNjgsfNVJkg4l8TblRjJv3E5ctRak,12339
google/cloud/firestore_v1/gapic_metadata.json,sha256=9gKxXCVZOEXa_n32e0FyRurohH5hmCj8bEEea1O754o,6391
google/cloud/firestore_v1/gapic_version.py,sha256=Q9GANDHaTnQvUDT7UUk1w_43IaPn8q7PucdTFHDod7o,653
google/cloud/firestore_v1/order.py,sha256=xZxijwPzlwxFnp7gKv1ujd5tPQ1Jd28QtHv1EhfxfaI,7026
google/cloud/firestore_v1/py.typed,sha256=9t7_u2uES6wHzMi1u2-nkdhxS1pAhtHKqubDWkRnMsQ,83
google/cloud/firestore_v1/query.py,sha256=mG8hhEcnaO_oP4P4u4zE0JLK5udoeDrSAiWBX5lfK3s,18043
google/cloud/firestore_v1/rate_limiter.py,sha256=cQm9IAQGPHGmeGWMH5nCQ94uZuEEoR9KqiV6PTLmghQ,6983
google/cloud/firestore_v1/services/__init__.py,sha256=zWbvQwgy48VXnYBlD4x4jlBof70BFYtIC9fehwEW1WQ,600
google/cloud/firestore_v1/services/__pycache__/__init__.cpython-38.pyc,,
google/cloud/firestore_v1/services/firestore/__init__.py,sha256=ftj_Q3J0YGoW8BWEKqFVkMOex3oqNktgbejts9wM4-s,749
google/cloud/firestore_v1/services/firestore/__pycache__/__init__.cpython-38.pyc,,
google/cloud/firestore_v1/services/firestore/__pycache__/async_client.cpython-38.pyc,,
google/cloud/firestore_v1/services/firestore/__pycache__/client.cpython-38.pyc,,
google/cloud/firestore_v1/services/firestore/__pycache__/pagers.cpython-38.pyc,,
google/cloud/firestore_v1/services/firestore/async_client.py,sha256=6e8GP3cRrVVZ119NWzDuarc-jdz-TLfXlLDW-IR6hmo,86836
google/cloud/firestore_v1/services/firestore/client.py,sha256=JNGfJzEdwoBD0M-lBQKSDuOgD0JHOyFD2iKOcTeCkuY,90283
google/cloud/firestore_v1/services/firestore/pagers.py,sha256=gsXzP02WfP_j7oFYylQ93_ZouMvcfS4wBoEMilQAqaI,15879
google/cloud/firestore_v1/services/firestore/transports/__init__.py,sha256=OAyUUXUT0Zx9qg8awSB0_zEl0AdlFpyGyZaJge1lVcs,1348
google/cloud/firestore_v1/services/firestore/transports/__pycache__/__init__.cpython-38.pyc,,
google/cloud/firestore_v1/services/firestore/transports/__pycache__/base.cpython-38.pyc,,
google/cloud/firestore_v1/services/firestore/transports/__pycache__/grpc.cpython-38.pyc,,
google/cloud/firestore_v1/services/firestore/transports/__pycache__/grpc_asyncio.cpython-38.pyc,,
google/cloud/firestore_v1/services/firestore/transports/__pycache__/rest.cpython-38.pyc,,
google/cloud/firestore_v1/services/firestore/transports/base.py,sha256=H7milYRdPlAkrTiReo7Cby3hfu3blxrXWDA-mKkS1MQ,21204
google/cloud/firestore_v1/services/firestore/transports/grpc.py,sha256=jIL16i731U1Td7h0OZ2AyWrCwZ_7V-ayrJm9ri6OlGk,33333
google/cloud/firestore_v1/services/firestore/transports/grpc_asyncio.py,sha256=fzXzyKoOiIyATy9nJu8pUsgQLtk36zhiDriTuYa_9vY,34001
google/cloud/firestore_v1/services/firestore/transports/rest.py,sha256=jb_b_wXLJxUi2T2SrnVKYlFMU7ZnWGpjoL4DK-iphfU,95533
google/cloud/firestore_v1/transaction.py,sha256=f3rSwOUB5oxKndBc4KaekK_xnrY2xYoE2co4K_9xfVs,14237
google/cloud/firestore_v1/transforms.py,sha256=rirIGlNeKHV-iWLyNC6t6ufJt2W4RbB1OMdA1qeIB-8,4685
google/cloud/firestore_v1/types/__init__.py,sha256=Krw_olMUGCOYJPlbnfYwNnkM_zk98_cbotBdnT0D5fg,3070
google/cloud/firestore_v1/types/__pycache__/__init__.cpython-38.pyc,,
google/cloud/firestore_v1/types/__pycache__/aggregation_result.cpython-38.pyc,,
google/cloud/firestore_v1/types/__pycache__/bloom_filter.cpython-38.pyc,,
google/cloud/firestore_v1/types/__pycache__/common.cpython-38.pyc,,
google/cloud/firestore_v1/types/__pycache__/document.cpython-38.pyc,,
google/cloud/firestore_v1/types/__pycache__/firestore.cpython-38.pyc,,
google/cloud/firestore_v1/types/__pycache__/query.cpython-38.pyc,,
google/cloud/firestore_v1/types/__pycache__/write.cpython-38.pyc,,
google/cloud/firestore_v1/types/aggregation_result.py,sha256=myWHNrnskOLr2Hj6UwvN9MFO9gRQnqD4CH4Tusbmjv0,1901
google/cloud/firestore_v1/types/bloom_filter.py,sha256=N-_iG74nW-QmlQXSXITX-DkZeLrLpZJ8OVBU-3AfRMI,3480
google/cloud/firestore_v1/types/common.py,sha256=vy1DGNtmetg71deu5r4XEI_8Kv4rUN3RB5Ty0Uu7gWs,5530
google/cloud/firestore_v1/types/document.py,sha256=L9hGmyjC3ayD2ZNMwiyF6Q3K5dEyRCKHIkJU6LzRuT0,9253
google/cloud/firestore_v1/types/firestore.py,sha256=VlTUShrJDe28Cc5S3IXVVL4iWEoSgf9QYpNqETbqLVA,59621
google/cloud/firestore_v1/types/query.py,sha256=GeE_Adv-ANXeDeefwkuscvSEEyCkjsDcV3kqUtKYGMs,27764
google/cloud/firestore_v1/types/write.py,sha256=nhPoy6hlKPKVyYl8e2rFfMdy2ERplGEj1Z5g0gBjjJs,19730
google/cloud/firestore_v1/watch.py,sha256=CHtlTUDNUxtuzabqdKVkPlfmmQL7XCnizX4Dn2ZITGQ,25513
google_cloud_firestore-2.13.1-py3.9-nspkg.pth,sha256=b0D5dZk3RUzK54tZ9iZDvLm7u8ltc5EzYrGCmhsuoNw,1698
google_cloud_firestore-2.13.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
google_cloud_firestore-2.13.1.dist-info/LICENSE,sha256=z8d0m5b2O9McPEK1xHG_dWgUBT6EfBDz6wA0F7xSPTA,11358
google_cloud_firestore-2.13.1.dist-info/METADATA,sha256=9HITWhTy2025Cz9wUyHJlzRTkTDu7xJhVlTQtDPA2Ps,5556
google_cloud_firestore-2.13.1.dist-info/RECORD,,
google_cloud_firestore-2.13.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
google_cloud_firestore-2.13.1.dist-info/WHEEL,sha256=bb2Ot9scclHKMOLDEHY6B2sicWOgugjFKaJsT7vwMQo,110
google_cloud_firestore-2.13.1.dist-info/namespace_packages.txt,sha256=v8IaYqRE2a0onAGJIpZeFkkH83wXSWZRR9eOyfMwoTc,20
google_cloud_firestore-2.13.1.dist-info/top_level.txt,sha256=_1QvSJIhFAGfxb79D6DhB7SUw2X6T4rwnz_LLrbcD3c,7
