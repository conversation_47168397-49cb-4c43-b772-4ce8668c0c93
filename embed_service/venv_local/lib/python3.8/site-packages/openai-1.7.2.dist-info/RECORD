../../../bin/openai,sha256=J0nuxAyt4-TMbz2WegV67AqMtgn1aRZaDoe9tGT2CfI,276
openai-1.7.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
openai-1.7.2.dist-info/METADATA,sha256=fl5LO_k6fRHJLrup2HQdR0u8ZSvNtaoHhNBJOdKGVu4,17357
openai-1.7.2.dist-info/RECORD,,
openai-1.7.2.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
openai-1.7.2.dist-info/WHEEL,sha256=mRYSEL3Ih6g5a_CVMIcwiF__0Ae4_gLYh01YFNwiq1k,87
openai-1.7.2.dist-info/entry_points.txt,sha256=kAYhQEmziJwsKs5raYAIOvJ2LWmbz5dulEXOzsY71ro,43
openai-1.7.2.dist-info/licenses/LICENSE,sha256=d0M6HDjQ76tf255XPlAGkIoECMe688MXcGEYsOFySfI,11336
openai/__init__.py,sha256=Fm96v4-wjPmUpPej-e1l3PsQ0bNuTb88ioeHCWzaDm0,9004
openai/__main__.py,sha256=bYt9eEaoRQWdejEHFD8REx9jxVEdZptECFsV7F49Ink,30
openai/__pycache__/__init__.cpython-38.pyc,,
openai/__pycache__/__main__.cpython-38.pyc,,
openai/__pycache__/_base_client.cpython-38.pyc,,
openai/__pycache__/_client.cpython-38.pyc,,
openai/__pycache__/_compat.cpython-38.pyc,,
openai/__pycache__/_constants.cpython-38.pyc,,
openai/__pycache__/_exceptions.cpython-38.pyc,,
openai/__pycache__/_files.cpython-38.pyc,,
openai/__pycache__/_models.cpython-38.pyc,,
openai/__pycache__/_module_client.cpython-38.pyc,,
openai/__pycache__/_qs.cpython-38.pyc,,
openai/__pycache__/_resource.cpython-38.pyc,,
openai/__pycache__/_response.cpython-38.pyc,,
openai/__pycache__/_streaming.cpython-38.pyc,,
openai/__pycache__/_types.cpython-38.pyc,,
openai/__pycache__/_version.cpython-38.pyc,,
openai/__pycache__/pagination.cpython-38.pyc,,
openai/__pycache__/version.cpython-38.pyc,,
openai/_base_client.py,sha256=H-S9fQK0RgQnYzt-UuksZ47igZ9jTKaIUKO-G66ythk,59202
openai/_client.py,sha256=34Ph2ArgeSqV8MlTzyDpH6I1TOP7y1zUGLr4vUurVok,18071
openai/_compat.py,sha256=Uy2y6JautGbCVfnCjn9JHOYD9ge1TvDIRWV_7TvcE44,5113
openai/_constants.py,sha256=cPUiQ4CPnBodn9VWw5Nz9z-obvNkGqKtSbg1W88Nb1o,382
openai/_exceptions.py,sha256=rWsrI3jGesc9-MkTmCMrfgE3xL_ew0lcmLHoFf-Y5n0,3614
openai/_extras/__init__.py,sha256=LZbJLZ7aFHRcI7uiY4-wFQTdMp-BF6FER1QMhKVFkWk,107
openai/_extras/__pycache__/__init__.cpython-38.pyc,,
openai/_extras/__pycache__/_common.cpython-38.pyc,,
openai/_extras/__pycache__/numpy_proxy.cpython-38.pyc,,
openai/_extras/__pycache__/pandas_proxy.cpython-38.pyc,,
openai/_extras/_common.py,sha256=NWWtgbdJsO3hQGQxaXGfVk0LjeIE5AFZ8VS_795hhMc,364
openai/_extras/numpy_proxy.py,sha256=nYeA0zUsLPsOYFoK-r3hr0MjG8QMBjyO3tU7a1KW8Js,850
openai/_extras/pandas_proxy.py,sha256=K-GJTaJiyG7Dq3A8-Hn1lCwX7UPFrhDVntI2Aref0Fo,688
openai/_files.py,sha256=Ow1uBpIr0bLnIHxtN-mB4Lsa4uVgNU6wLVOkgqi9Gzc,3470
openai/_models.py,sha256=w3FScbvW3ipc94D1v0xKm6kxYNtlbrvJxRfIR18OxZU,16410
openai/_module_client.py,sha256=DT9hNYHiZ4UzibIA4xKF3BXmHrujTLO4j-jLFjlfJXI,2280
openai/_qs.py,sha256=AOkSz4rHtK4YI3ZU_kzea-zpwBUgEY8WniGmTPyEimc,4846
openai/_resource.py,sha256=F_pXU86CNv_FFBPjW7Pwcr4wOkcBaEEkoeQOGrwLlXY,1070
openai/_response.py,sha256=jbkmurpzt9Bf29wdVYdRu8P-n_ww-ombmiSoxUDEbos,9369
openai/_streaming.py,sha256=ImWFF1UGMl7uoe4Ysm1FUP7dvUGVQQubJtoJfO5uaQs,8010
openai/_types.py,sha256=w29WIu3uxFmcB-pcETbp6Q3n6Rb8jq1w464Oh3ZGgqw,10077
openai/_utils/__init__.py,sha256=rNocEdenOk45DFWPBJN2Otr3hTiW5U7JWK2aSEqklJk,1613
openai/_utils/__pycache__/__init__.cpython-38.pyc,,
openai/_utils/__pycache__/_logs.cpython-38.pyc,,
openai/_utils/__pycache__/_proxy.cpython-38.pyc,,
openai/_utils/__pycache__/_streams.cpython-38.pyc,,
openai/_utils/__pycache__/_transform.cpython-38.pyc,,
openai/_utils/__pycache__/_typing.cpython-38.pyc,,
openai/_utils/__pycache__/_utils.cpython-38.pyc,,
openai/_utils/_logs.py,sha256=sFA_NejuNObTGGbfsXC03I38mrT9HjsgAJx4d3GP0ok,774
openai/_utils/_proxy.py,sha256=SyQdO2oTSPGXj2ACEwYl520EMr4hh6hl1fIOuJ9ebes,2302
openai/_utils/_streams.py,sha256=SMC90diFFecpEg_zgDRVbdR3hSEIgVVij4taD-noMLM,289
openai/_utils/_transform.py,sha256=CaC2j4Ezo4W7AyQZDwaI5UrhDLqTdYtBP2bVK4Jt0Rw,7029
openai/_utils/_typing.py,sha256=-japjP-CqMESriJzRV5Ly2KGUqPzNcoX4qSv_gRmCRo,2569
openai/_utils/_utils.py,sha256=t-OLnWJsgZuwU3O_U9l8WLTi_ueMii5oHArztC3IyZ0,11005
openai/_version.py,sha256=TKWBblfaJ1HQXbs5mSV4xGo58GihilfDwowiWuz740E,125
openai/cli/__init__.py,sha256=soGgtqyomgddl92H0KJRqHqGuaXIaghq86qkzLuVp7U,31
openai/cli/__pycache__/__init__.cpython-38.pyc,,
openai/cli/__pycache__/_cli.cpython-38.pyc,,
openai/cli/__pycache__/_errors.cpython-38.pyc,,
openai/cli/__pycache__/_models.cpython-38.pyc,,
openai/cli/__pycache__/_progress.cpython-38.pyc,,
openai/cli/__pycache__/_utils.cpython-38.pyc,,
openai/cli/_api/__init__.py,sha256=cj92MZq-9_1PQM8A4TQVsqKn5mcTDAGxHllJ0UvJOPE,58
openai/cli/_api/__pycache__/__init__.cpython-38.pyc,,
openai/cli/_api/__pycache__/_main.cpython-38.pyc,,
openai/cli/_api/__pycache__/audio.cpython-38.pyc,,
openai/cli/_api/__pycache__/completions.cpython-38.pyc,,
openai/cli/_api/__pycache__/files.cpython-38.pyc,,
openai/cli/_api/__pycache__/image.cpython-38.pyc,,
openai/cli/_api/__pycache__/models.cpython-38.pyc,,
openai/cli/_api/_main.py,sha256=5yyfLURqCEaAN8B61gHaqVAaYgtyb9Xq0ncQ3P2BAh0,451
openai/cli/_api/audio.py,sha256=HZDTRZT-qZTMsg7WOm-djCQlf874aSa3lxRvNG27wLM,3347
openai/cli/_api/chat/__init__.py,sha256=MhFUQH9F6QCtbPMlbsU_DWTd7wc5DSCZ7Wy3FBGVij0,300
openai/cli/_api/chat/__pycache__/__init__.cpython-38.pyc,,
openai/cli/_api/chat/__pycache__/completions.cpython-38.pyc,,
openai/cli/_api/chat/completions.py,sha256=9Ztetyz7rm0gP5SOPWEcpzFJnJKuIEQit626vOq42bE,5363
openai/cli/_api/completions.py,sha256=ysOmnbXpFz3VB5N_5USPdObiYew62vEn6rMtNFwTJGQ,6412
openai/cli/_api/files.py,sha256=6nKXFnsC2QE0bGnVUAG7BTLSu6K1_MhPE0ZJACmzgRY,2345
openai/cli/_api/image.py,sha256=VKMRqKCHkl4JO7uP7RLDZu8DzF6ddQgpr3n2v9EOEBk,4711
openai/cli/_api/models.py,sha256=pGmIGZToj3raGGpKvPSq_EVUR-dqg4Vi0PNfZH98D2E,1295
openai/cli/_cli.py,sha256=WxqTnhVVtfzX0z7hV5fcvd3hkihaUgwOWpXOwyCS4Fc,6743
openai/cli/_errors.py,sha256=7BYF2Kp_L6yKsZDNdg-gK71FMVCNjhrunfVVgh4Zy0M,479
openai/cli/_models.py,sha256=tgsldjG216KpwgAZ5pS0sV02FQvONDJU2ElA4kCCiIU,491
openai/cli/_progress.py,sha256=aMLssU9jh-LoqRYH3608jNos7r6vZKnHTRlHxFznzv4,1406
openai/cli/_tools/__init__.py,sha256=cj92MZq-9_1PQM8A4TQVsqKn5mcTDAGxHllJ0UvJOPE,58
openai/cli/_tools/__pycache__/__init__.cpython-38.pyc,,
openai/cli/_tools/__pycache__/_main.cpython-38.pyc,,
openai/cli/_tools/__pycache__/fine_tunes.cpython-38.pyc,,
openai/cli/_tools/__pycache__/migrate.cpython-38.pyc,,
openai/cli/_tools/_main.py,sha256=pakjEXHRHqYlTml-RxV7fNrRtRXzmZBinoPi1AJipFY,467
openai/cli/_tools/fine_tunes.py,sha256=RQgYMzifk6S7Y1I1K6huqco2QxmXa7gVUlHl6SrKTSU,1543
openai/cli/_tools/migrate.py,sha256=PqJgT-7KrCiue5Wj-1YaXfKNqcGFHXnrC9EU4eHX39c,4895
openai/cli/_utils.py,sha256=oiTc9MnxQh_zxAZ1OIHPkoDpCll0NF9ZgkdFHz4T-Bs,848
openai/lib/.keep,sha256=wuNrz-5SXo3jJaJOJgz4vFHM41YH_g20F5cRQo0vLes,224
openai/lib/__pycache__/_old_api.cpython-38.pyc,,
openai/lib/__pycache__/_validators.cpython-38.pyc,,
openai/lib/__pycache__/azure.cpython-38.pyc,,
openai/lib/_old_api.py,sha256=XZnXBrEKuTd70iJirj5mGW35fZoqruJobbBTq6bvg10,1947
openai/lib/_validators.py,sha256=jnVLH1mIN1zumudXyxv1UjyXJpd7FLIU719wiRIBues,35189
openai/lib/azure.py,sha256=5e4SHv3nN0xoIOBWV1hvoOLkhCyaXpdX6fQtPXBMxh8,20924
openai/pagination.py,sha256=t6ibEYkuAueBYQttxq3hUTGrglSRQBJi2uP0i--fZhY,2727
openai/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
openai/resources/__init__.py,sha256=OvvQ0B562V8qfIJDS0HxJiG9m9esBnXqM4LhVxUQJhs,2048
openai/resources/__pycache__/__init__.cpython-38.pyc,,
openai/resources/__pycache__/completions.cpython-38.pyc,,
openai/resources/__pycache__/embeddings.cpython-38.pyc,,
openai/resources/__pycache__/files.cpython-38.pyc,,
openai/resources/__pycache__/images.cpython-38.pyc,,
openai/resources/__pycache__/models.cpython-38.pyc,,
openai/resources/__pycache__/moderations.cpython-38.pyc,,
openai/resources/audio/__init__.py,sha256=r7U8yW8SXrxCdEvwt0haZVoUcAaqCSuLjx2JJoYtkVM,955
openai/resources/audio/__pycache__/__init__.cpython-38.pyc,,
openai/resources/audio/__pycache__/audio.cpython-38.pyc,,
openai/resources/audio/__pycache__/speech.cpython-38.pyc,,
openai/resources/audio/__pycache__/transcriptions.cpython-38.pyc,,
openai/resources/audio/__pycache__/translations.cpython-38.pyc,,
openai/resources/audio/audio.py,sha256=wLmq_UJLD8-z6zjv3opc7nZu91gVCUoFrUxjCOcuhSA,2170
openai/resources/audio/speech.py,sha256=T9Xa24ao-_GdazKxi5Hqmum6lf6MFQJsQXOJh7zGYjY,6451
openai/resources/audio/transcriptions.py,sha256=dFimiP3CA3-xuNXVPaY6APL8wL8x_NSbdKRvsoyfO6M,8682
openai/resources/audio/translations.py,sha256=s4qDpeHeeChcbBQ1aLZK-b99eBci47UavWh9vkXh39o,7830
openai/resources/beta/__init__.py,sha256=NhKclrsI6kvMpkqQNurrzlyei7E1Q-Ewc41GlR-A4tA,657
openai/resources/beta/__pycache__/__init__.cpython-38.pyc,,
openai/resources/beta/__pycache__/beta.cpython-38.pyc,,
openai/resources/beta/assistants/__init__.py,sha256=PWQ5TfkfidCr0ziYfGmXXvpl3jztD_lrorzKwPe6JVk,470
openai/resources/beta/assistants/__pycache__/__init__.cpython-38.pyc,,
openai/resources/beta/assistants/__pycache__/assistants.cpython-38.pyc,,
openai/resources/beta/assistants/__pycache__/files.cpython-38.pyc,,
openai/resources/beta/assistants/assistants.py,sha256=bZmHcRt74jFUhuyD6GwZX9k1CQCW8SgcHS3R6iFJCR8,27416
openai/resources/beta/assistants/files.py,sha256=glSPV9U6mub3sqQXIwHDoLjcO6f-JlzOtnn8-vFu3HM,16350
openai/resources/beta/beta.py,sha256=vaYpEDkhQqH0qde7Bs-wAvFeuW2mLAFJTlOICwAU-hA,1690
openai/resources/beta/threads/__init__.py,sha256=HVAzvEjpMgL00Zvhv99fQyuEq_WkW6LjwlRHQnbjh9s,639
openai/resources/beta/threads/__pycache__/__init__.cpython-38.pyc,,
openai/resources/beta/threads/__pycache__/threads.cpython-38.pyc,,
openai/resources/beta/threads/messages/__init__.py,sha256=8M_0ZMpHEWUrB0kJ1Mx4t1g8w9k5-98X3HNTiLDV4R8,452
openai/resources/beta/threads/messages/__pycache__/__init__.cpython-38.pyc,,
openai/resources/beta/threads/messages/__pycache__/files.cpython-38.pyc,,
openai/resources/beta/threads/messages/__pycache__/messages.cpython-38.pyc,,
openai/resources/beta/threads/messages/files.py,sha256=xexsgYQMAaLJ8BotpVkaUtdrBhVSENAFNSSbT53HsZI,9922
openai/resources/beta/threads/messages/messages.py,sha256=0KMdmf8O3x14fQLJtaOpjui2GmuijP8toFTuKvQDfF4,19195
openai/resources/beta/threads/runs/__init__.py,sha256=JDJA_Qyv3V1kDQ56HdWd3G92m1yc5otzJf8G-B3sd4w,416
openai/resources/beta/threads/runs/__pycache__/__init__.cpython-38.pyc,,
openai/resources/beta/threads/runs/__pycache__/runs.cpython-38.pyc,,
openai/resources/beta/threads/runs/__pycache__/steps.cpython-38.pyc,,
openai/resources/beta/threads/runs/runs.py,sha256=OTvBYyk7AXSRiiQsihvz-LHXsZPk0nWV4JzPQeZh9us,27306
openai/resources/beta/threads/runs/steps.py,sha256=Rh40WWuanmvgLFRHauK9m8BaLRckCpqGSFOvq8mooSI,9844
openai/resources/beta/threads/threads.py,sha256=rry0RmvBqYEqaHu8GbFP_NZOnovqYKlEWZmAlkPVSGI,21637
openai/resources/chat/__init__.py,sha256=-hNKY-g-GApTUUHvt7bnI4lgmZiyaBelb5ciAYAeT_0,470
openai/resources/chat/__pycache__/__init__.cpython-38.pyc,,
openai/resources/chat/__pycache__/chat.cpython-38.pyc,,
openai/resources/chat/__pycache__/completions.cpython-38.pyc,,
openai/resources/chat/chat.py,sha256=PiBGFSpVkcuDt0mMb-2UX9c9aoNr84jeuHzQXhsMaq4,1176
openai/resources/chat/completions.py,sha256=Jko5guHFX0FMpEZeU88RwpJuFMdaW9lLrQ4xPeSUCDg,69491
openai/resources/completions.py,sha256=iBUc3ntQcKM8gk9LfwPo_w--JJD55tAYry5LFH50Op0,55524
openai/resources/embeddings.py,sha256=0JFqwtLCRhjMCjymnlY38WRZT9D3QtyN74JGPHgbGhk,9107
openai/resources/files.py,sha256=dDk7AZKeKob109RU6GfBT7kR8h5i9q3HK2fUMqNTaMo,22477
openai/resources/fine_tuning/__init__.py,sha256=gcx8ire5kJ4_2SUPsV-__8iRKlWCptxrPgZNJgSR8oU,462
openai/resources/fine_tuning/__pycache__/__init__.cpython-38.pyc,,
openai/resources/fine_tuning/__pycache__/fine_tuning.cpython-38.pyc,,
openai/resources/fine_tuning/__pycache__/jobs.cpython-38.pyc,,
openai/resources/fine_tuning/fine_tuning.py,sha256=pHKaVNU7MxxH8BdKOQ26BQgfGJ3ouOzCY2GKnPl_ADc,1157
openai/resources/fine_tuning/jobs.py,sha256=CtkkqMuQH8VbflltBeKeD_wtpmLR-wo_3h8_SXhIGG4,21826
openai/resources/images.py,sha256=0G2x8oITS8hfqbGZ8ZVZBeH7Z7XXOaJIQBixxtOIbIk,22831
openai/resources/models.py,sha256=GazqEbuc2iXT4Fw_lbLqVs4CRPrLK7JmbSkwR9b-Kfg,8390
openai/resources/moderations.py,sha256=TODaDmJBmEFoLMsN2TQJd442KnkwruciDhhG-7hlGX4,5620
openai/types/__init__.py,sha256=_gd3v34AgdGTjDjZ8hPLw_AFhMdZ1MTB1pmcI_m-brQ,1629
openai/types/__pycache__/__init__.cpython-38.pyc,,
openai/types/__pycache__/completion.cpython-38.pyc,,
openai/types/__pycache__/completion_choice.cpython-38.pyc,,
openai/types/__pycache__/completion_create_params.cpython-38.pyc,,
openai/types/__pycache__/completion_usage.cpython-38.pyc,,
openai/types/__pycache__/create_embedding_response.cpython-38.pyc,,
openai/types/__pycache__/embedding.cpython-38.pyc,,
openai/types/__pycache__/embedding_create_params.cpython-38.pyc,,
openai/types/__pycache__/file_content.cpython-38.pyc,,
openai/types/__pycache__/file_create_params.cpython-38.pyc,,
openai/types/__pycache__/file_deleted.cpython-38.pyc,,
openai/types/__pycache__/file_list_params.cpython-38.pyc,,
openai/types/__pycache__/file_object.cpython-38.pyc,,
openai/types/__pycache__/image.cpython-38.pyc,,
openai/types/__pycache__/image_create_variation_params.cpython-38.pyc,,
openai/types/__pycache__/image_edit_params.cpython-38.pyc,,
openai/types/__pycache__/image_generate_params.cpython-38.pyc,,
openai/types/__pycache__/images_response.cpython-38.pyc,,
openai/types/__pycache__/model.cpython-38.pyc,,
openai/types/__pycache__/model_deleted.cpython-38.pyc,,
openai/types/__pycache__/moderation.cpython-38.pyc,,
openai/types/__pycache__/moderation_create_params.cpython-38.pyc,,
openai/types/__pycache__/moderation_create_response.cpython-38.pyc,,
openai/types/audio/__init__.py,sha256=afWI555-qErp31UCqx-lW6Wu2fmje8M-R0E_zQhIAio,461
openai/types/audio/__pycache__/__init__.cpython-38.pyc,,
openai/types/audio/__pycache__/speech_create_params.cpython-38.pyc,,
openai/types/audio/__pycache__/transcription.cpython-38.pyc,,
openai/types/audio/__pycache__/transcription_create_params.cpython-38.pyc,,
openai/types/audio/__pycache__/translation.cpython-38.pyc,,
openai/types/audio/__pycache__/translation_create_params.cpython-38.pyc,,
openai/types/audio/speech_create_params.py,sha256=nvRpp2soI3ljAdR_XE5ally2AHYvdBM_fSrapeKd6bQ,1207
openai/types/audio/transcription.py,sha256=bHHqJzRrdk_p23yvenTGjw0QBnjI3ebem1Ji1IJROOc,164
openai/types/audio/transcription_create_params.py,sha256=H-OP-3omPi9zE3qkAkU-GOuctMJyqiRn6mg64Jx9Yqc,1694
openai/types/audio/translation.py,sha256=qOh5RFGsAjX5fid6vFYtdNNO_7QVtAAZWzHwbcNc224,160
openai/types/audio/translation_create_params.py,sha256=xxG6-xycSd8Zzt6yak1fYUa7BbbwOKxdVzWRbOYUE0A,1404
openai/types/beta/__init__.py,sha256=ww4Yg-cSessqkFW8oYVlLNYRzOfBb5rJiMFtm4gWIg4,791
openai/types/beta/__pycache__/__init__.cpython-38.pyc,,
openai/types/beta/__pycache__/assistant.cpython-38.pyc,,
openai/types/beta/__pycache__/assistant_create_params.cpython-38.pyc,,
openai/types/beta/__pycache__/assistant_deleted.cpython-38.pyc,,
openai/types/beta/__pycache__/assistant_list_params.cpython-38.pyc,,
openai/types/beta/__pycache__/assistant_update_params.cpython-38.pyc,,
openai/types/beta/__pycache__/thread.cpython-38.pyc,,
openai/types/beta/__pycache__/thread_create_and_run_params.cpython-38.pyc,,
openai/types/beta/__pycache__/thread_create_params.cpython-38.pyc,,
openai/types/beta/__pycache__/thread_deleted.cpython-38.pyc,,
openai/types/beta/__pycache__/thread_update_params.cpython-38.pyc,,
openai/types/beta/assistant.py,sha256=lal618IBFSPftBegKAIQGpIvho5d1kAvPWJOJ5rJPEw,2587
openai/types/beta/assistant_create_params.py,sha256=iBySbs07eMtetqfYlqgekXKsqxaPaK7Jg61RtFTFx-k,2539
openai/types/beta/assistant_deleted.py,sha256=fV1wGdYtmF8irj2WRf6eR24JodG0z1rM_M5bwqSvoV4,268
openai/types/beta/assistant_list_params.py,sha256=gsSRtdvZMiBxaTCvpfXy3LYKzViPl0ysx9a8tDguxf8,1187
openai/types/beta/assistant_update_params.py,sha256=DarGVjG3dZpW3uIRZEwXxtkwCTPuTohVeaviPZTZoT0,2656
openai/types/beta/assistants/__init__.py,sha256=nPm_I-yeVKKJRYqWzGrf9OoVDDDsg1c0Qj936gIEKkk,356
openai/types/beta/assistants/__pycache__/__init__.cpython-38.pyc,,
openai/types/beta/assistants/__pycache__/assistant_file.cpython-38.pyc,,
openai/types/beta/assistants/__pycache__/file_create_params.cpython-38.pyc,,
openai/types/beta/assistants/__pycache__/file_delete_response.cpython-38.pyc,,
openai/types/beta/assistants/__pycache__/file_list_params.cpython-38.pyc,,
openai/types/beta/assistants/assistant_file.py,sha256=RLht24Zg-XbpuKZk2aVXFTH0R0_2c8smvfice7Gekxs,554
openai/types/beta/assistants/file_create_params.py,sha256=J09q5nl2ZGJ4y8ShBJ5ur1PS7XGrNOBSBz0ptzX2-LM,484
openai/types/beta/assistants/file_delete_response.py,sha256=unPwyynR71X4Ap0nJ4KENk2JGOv65SVLiavUVcLTVAc,278
openai/types/beta/assistants/file_list_params.py,sha256=J-AQx5Eu1fgMjHOUBl3u1HGEDkANVRrmIUk4x7EXTN8,1177
openai/types/beta/chat/__init__.py,sha256=WirADquEnYGjBTTLlIC-kNtGf6opfip3z-sKplC_k3Y,89
openai/types/beta/chat/__pycache__/__init__.cpython-38.pyc,,
openai/types/beta/thread.py,sha256=0ZD-g1I7DzxGa8sCYbDvKVtZwdzWN_duWe7JQV2jsyE,806
openai/types/beta/thread_create_and_run_params.py,sha256=_0KdAovrcW_oj-AuOq-WZMqbsN-sTRj-vtotDoUVx_M,3722
openai/types/beta/thread_create_params.py,sha256=U_YyCEhqNkGW_a7_MEmV1udHj5UiLtv3DvIrPxfQeGM,1617
openai/types/beta/thread_deleted.py,sha256=jrDuKYAP-oKp3-5ZvjoPR4Yr9aZ-VhWbl5vijDdtypA,259
openai/types/beta/thread_update_params.py,sha256=ElYwOy-_AWDgKH_LRJddQO9saiWMiEMQuDK4MrKMeLQ,554
openai/types/beta/threads/__init__.py,sha256=3xm8RjgLAieXgrJOlD1KUUp3dERW8ExWtwxy8DSq3Tg,977
openai/types/beta/threads/__pycache__/__init__.cpython-38.pyc,,
openai/types/beta/threads/__pycache__/message_content_image_file.cpython-38.pyc,,
openai/types/beta/threads/__pycache__/message_content_text.cpython-38.pyc,,
openai/types/beta/threads/__pycache__/message_create_params.cpython-38.pyc,,
openai/types/beta/threads/__pycache__/message_list_params.cpython-38.pyc,,
openai/types/beta/threads/__pycache__/message_update_params.cpython-38.pyc,,
openai/types/beta/threads/__pycache__/required_action_function_tool_call.cpython-38.pyc,,
openai/types/beta/threads/__pycache__/run.cpython-38.pyc,,
openai/types/beta/threads/__pycache__/run_create_params.cpython-38.pyc,,
openai/types/beta/threads/__pycache__/run_list_params.cpython-38.pyc,,
openai/types/beta/threads/__pycache__/run_submit_tool_outputs_params.cpython-38.pyc,,
openai/types/beta/threads/__pycache__/run_update_params.cpython-38.pyc,,
openai/types/beta/threads/__pycache__/thread_message.cpython-38.pyc,,
openai/types/beta/threads/message_content_image_file.py,sha256=OT2xpdVLxorivEtN6wvkBlcA3tl0BBHSp80dknEsJcY,489
openai/types/beta/threads/message_content_text.py,sha256=e4eIkUfqJDGBfYPCfDHrpA3YDr6fkEnZJLVQ8KaW5Bo,1575
openai/types/beta/threads/message_create_params.py,sha256=y7Vj9uVZ_61vwPCpyBDOzrQiycIRrBj4Wvk1ZzZ1_B4,1100
openai/types/beta/threads/message_list_params.py,sha256=GL2Hq8PoEFSbmQ4DVtCtgFSqc8VoFs7ftvVWIZcybkA,1183
openai/types/beta/threads/message_update_params.py,sha256=Zy46a0o5rxrde_REpNZPmx4G89BMP5DHk1E5cMuUNVw,596
openai/types/beta/threads/messages/__init__.py,sha256=NXH8z8CPb2Bhr_delxNMpdmzFLxaUnVyz7TX2GdTpbc,206
openai/types/beta/threads/messages/__pycache__/__init__.cpython-38.pyc,,
openai/types/beta/threads/messages/__pycache__/file_list_params.cpython-38.pyc,,
openai/types/beta/threads/messages/__pycache__/message_file.cpython-38.pyc,,
openai/types/beta/threads/messages/file_list_params.py,sha256=L6pM_ZzxD_N2dPTeuBhe7FzkKbrXL1lF0gmGAjP_lN8,1217
openai/types/beta/threads/messages/message_file.py,sha256=ZzehFlMaprtmwq4zLx8frhA6APldpexoulNQ4oMxYGU,695
openai/types/beta/threads/required_action_function_tool_call.py,sha256=yuF4lNm5UEq8sMR96BeSCDD-C3kdQcbcf2A7VbZwVJ0,855
openai/types/beta/threads/run.py,sha256=erCrQp2GFZ2zqN9kgnpqgblRfZQdWUK1ZUBO__WU7K4,4500
openai/types/beta/threads/run_create_params.py,sha256=X1S0BXjkq5H4zls0WgBXIdK2YowU2WWSvLvLnsFMpUI,2512
openai/types/beta/threads/run_list_params.py,sha256=TMAVh4Ffwzkx4WW1-L6ezfCB7LY-u2mdLP5phAvxd-A,1175
openai/types/beta/threads/run_submit_tool_outputs_params.py,sha256=_Zvt4gUhFDdHHX0VXPBlH0J8Dj2QAS-i5KqfIaeC_QQ,719
openai/types/beta/threads/run_update_params.py,sha256=lTdDYumU09-BzihovwaUWy_7em9l6d9DXiabK-j60wM,588
openai/types/beta/threads/runs/__init__.py,sha256=EyHtFKh0sIyLw-raVzV3c9SkVkaKj4ZvpM1e_V4NnAo,574
openai/types/beta/threads/runs/__pycache__/__init__.cpython-38.pyc,,
openai/types/beta/threads/runs/__pycache__/code_tool_call.cpython-38.pyc,,
openai/types/beta/threads/runs/__pycache__/function_tool_call.cpython-38.pyc,,
openai/types/beta/threads/runs/__pycache__/message_creation_step_details.cpython-38.pyc,,
openai/types/beta/threads/runs/__pycache__/retrieval_tool_call.cpython-38.pyc,,
openai/types/beta/threads/runs/__pycache__/run_step.cpython-38.pyc,,
openai/types/beta/threads/runs/__pycache__/step_list_params.cpython-38.pyc,,
openai/types/beta/threads/runs/__pycache__/tool_calls_step_details.cpython-38.pyc,,
openai/types/beta/threads/runs/code_tool_call.py,sha256=c_C8t3FkwkYehflYYBWPKKbracIcF48EI4xiYHBHicU,1632
openai/types/beta/threads/runs/function_tool_call.py,sha256=HwfEELO0Ck8x-ddcIKCpKRgVOUJn_jj_jtiLb7tAVLI,887
openai/types/beta/threads/runs/message_creation_step_details.py,sha256=Ujxn44Aj27RS3m14Q7qe8t7e1izhSHceqLSoYJQxXP0,473
openai/types/beta/threads/runs/retrieval_tool_call.py,sha256=X4DgXBggLkbkxNbVy74Cot0TtzWdZv2WKtTXeHawbQY,481
openai/types/beta/threads/runs/run_step.py,sha256=2bB9MOfEbNS06O4LFvcvM_c4O6lR-XfXQhAQAmhUMos,2850
openai/types/beta/threads/runs/step_list_params.py,sha256=4RjV9cvUVIESZr7CWkIJbw2T-D9dCmW2uiHCkdh7nRg,1217
openai/types/beta/threads/runs/tool_calls_step_details.py,sha256=WiZ3no8GpAFT26kBybsEPHnd9TyufhuIZXLDOew-2Ig,736
openai/types/beta/threads/thread_message.py,sha256=pEuvKqJrCg06E5_afimOlaE2d8YzoszoA7wGXLS3wiM,2082
openai/types/chat/__init__.py,sha256=ISnJHqpzjSN8VA4oBDt0vfMGwg9e-g7y5M8QuPRYh3M,2431
openai/types/chat/__pycache__/__init__.cpython-38.pyc,,
openai/types/chat/__pycache__/chat_completion.cpython-38.pyc,,
openai/types/chat/__pycache__/chat_completion_assistant_message_param.cpython-38.pyc,,
openai/types/chat/__pycache__/chat_completion_chunk.cpython-38.pyc,,
openai/types/chat/__pycache__/chat_completion_content_part_image_param.cpython-38.pyc,,
openai/types/chat/__pycache__/chat_completion_content_part_param.cpython-38.pyc,,
openai/types/chat/__pycache__/chat_completion_content_part_text_param.cpython-38.pyc,,
openai/types/chat/__pycache__/chat_completion_function_call_option_param.cpython-38.pyc,,
openai/types/chat/__pycache__/chat_completion_function_message_param.cpython-38.pyc,,
openai/types/chat/__pycache__/chat_completion_message.cpython-38.pyc,,
openai/types/chat/__pycache__/chat_completion_message_param.cpython-38.pyc,,
openai/types/chat/__pycache__/chat_completion_message_tool_call.cpython-38.pyc,,
openai/types/chat/__pycache__/chat_completion_message_tool_call_param.cpython-38.pyc,,
openai/types/chat/__pycache__/chat_completion_named_tool_choice_param.cpython-38.pyc,,
openai/types/chat/__pycache__/chat_completion_role.cpython-38.pyc,,
openai/types/chat/__pycache__/chat_completion_system_message_param.cpython-38.pyc,,
openai/types/chat/__pycache__/chat_completion_token_logprob.cpython-38.pyc,,
openai/types/chat/__pycache__/chat_completion_tool_choice_option_param.cpython-38.pyc,,
openai/types/chat/__pycache__/chat_completion_tool_message_param.cpython-38.pyc,,
openai/types/chat/__pycache__/chat_completion_tool_param.cpython-38.pyc,,
openai/types/chat/__pycache__/chat_completion_user_message_param.cpython-38.pyc,,
openai/types/chat/__pycache__/completion_create_params.cpython-38.pyc,,
openai/types/chat/chat_completion.py,sha256=v1CzdPdI2x3kip08cvvVI9JE-fPT1YQNMUdrkndSIt4,2288
openai/types/chat/chat_completion_assistant_message_param.py,sha256=urtNJ-k4hzw4ZE3KbDT1wzMiUcjYjc12sUM8K0SyVbg,1597
openai/types/chat/chat_completion_chunk.py,sha256=S6fcNkl2eopGsE7gKwIPAyscIvxRlkgm0K-962NRits,4062
openai/types/chat/chat_completion_content_part_image_param.py,sha256=kyqp63mKKPtIdepl8kNrJEaoP8mQvLrLBjDiIK7PCPY,764
openai/types/chat/chat_completion_content_part_param.py,sha256=Edlt5NGiNc2sRo7WC2w3GQUy4VmKw5t1nj1WxmEx4b8,453
openai/types/chat/chat_completion_content_part_text_param.py,sha256=_TJtxbHQaU6-I5uEoriVI7rseY7KNdHV9ouA6wJH9f4,396
openai/types/chat/chat_completion_function_call_option_param.py,sha256=3FGW-vOFJ41UYqiiQujJn-M4uGt2MLHEVfzfWVLS0_I,332
openai/types/chat/chat_completion_function_message_param.py,sha256=KB4cXHg__FkweLmXTCg1L7crLOSKsn-fcqtRByXomRc,558
openai/types/chat/chat_completion_message.py,sha256=irtHQZHRFgsEk2jrxfbET5tERmPa51cTGa0IS28_rdQ,1249
openai/types/chat/chat_completion_message_param.py,sha256=hNVkRJmP29bjIidWBv6KLIaF9360HACjlNPMQ8UacZc,805
openai/types/chat/chat_completion_message_tool_call.py,sha256=2GS49NL6M9bx3yoFTRrZBtA5bGXMV6Vz87HDLzrVPxE,867
openai/types/chat/chat_completion_message_tool_call_param.py,sha256=TPTxAapX7imm3Je3IxLYZCyvmkP4QYH0ad0wsk1fLlg,976
openai/types/chat/chat_completion_named_tool_choice_param.py,sha256=eZ1mFBPOOavrBLXHpLz1KsnIWfVZiZyYlhWzz9VCgmU,536
openai/types/chat/chat_completion_role.py,sha256=na9kKLwNySYF4CDeuJKx8bo1lc20PHxe46oXHLAb8cY,207
openai/types/chat/chat_completion_system_message_param.py,sha256=WORNNRGXGOUL6JsN6JO19PY7dna50e6ktl5IyhnTFU0,605
openai/types/chat/chat_completion_token_logprob.py,sha256=BPsN1DGpX7Vyp2IjnYInXr80CDRXO2C5i5b8FeeuV4M,1440
openai/types/chat/chat_completion_tool_choice_option_param.py,sha256=h52I05ob9-LzmY9ssGLzsH_Er5-wR0KH85Ud991l1yM,399
openai/types/chat/chat_completion_tool_message_param.py,sha256=zbH8uNKaiETcJSitUBZ7BWtnD11t3NVIUzqpyan0Qkw,520
openai/types/chat/chat_completion_tool_param.py,sha256=ez93XyQd_u0lAy40cO5aiJuboCNtvWyAZA8JhKFVURY,452
openai/types/chat/chat_completion_user_message_param.py,sha256=bg6vL-RWy48X-EB02TENMU6l4wueN5_5oQk5ddvdR5U,751
openai/types/chat/completion_create_params.py,sha256=ier0Rxwh7-9UidSrMovWyQmPyyZpQZ0n4dOoGNZ38HA,10362
openai/types/completion.py,sha256=TWZXMOLfKcZL_kKDgyJAiZ34_nFQiB66ts0svtp02so,1139
openai/types/completion_choice.py,sha256=lEtW_yLa1PUIt0IbEKnjnuu0iuqNOTRsCRRwmkFFBfM,932
openai/types/completion_create_params.py,sha256=Gxc0Ll1z_TFNnJLdxWYUbUgVaQRfaT9ZdbfJHxGgtc4,7201
openai/types/completion_usage.py,sha256=H1onC3vuVy5NKSUlW8FBfVzl_PfgFyrB_ytro80ZAjM,401
openai/types/create_embedding_response.py,sha256=quvWyAE48qV_pwTyWTBT7OO-t1AF0zZaMUI8n-PPtCI,765
openai/types/embedding.py,sha256=EPMqrP_OjLMuiTv0XcLTizicilcdgAw16akbdWD7xIc,604
openai/types/embedding_create_params.py,sha256=aN3Y0hmW11Af2boTiWxnT4VikuhTJnHUKlmsTb6RIxg,1612
openai/types/file_content.py,sha256=GuNtqtZAuHFXS0uIJbTpOvzx_RePrE-lnH3FvNw4dJ8,100
openai/types/file_create_params.py,sha256=4F6XpTDTRGXZjusxXbU-82rvxFAUju04IC6PywtDTYU,840
openai/types/file_deleted.py,sha256=cJQWjVK1EiFj3kwV4ndQnF5Mlqv7ZSXNJtSwEWilL-A,244
openai/types/file_list_params.py,sha256=0IpTyqCAMcMl-Q2W1v-4J_xpoCdSmh1zvv_p-X0OmEQ,277
openai/types/file_object.py,sha256=owuX6HgYMquwzCif65pNSUV-5dtYs09wtjk0ONnfvME,1193
openai/types/fine_tuning/__init__.py,sha256=wfu8ciqHopBRPUkZ0D_NkP14-1wYFgVrY8pbCI2i84s,431
openai/types/fine_tuning/__pycache__/__init__.cpython-38.pyc,,
openai/types/fine_tuning/__pycache__/fine_tuning_job.cpython-38.pyc,,
openai/types/fine_tuning/__pycache__/fine_tuning_job_event.cpython-38.pyc,,
openai/types/fine_tuning/__pycache__/job_create_params.cpython-38.pyc,,
openai/types/fine_tuning/__pycache__/job_list_events_params.cpython-38.pyc,,
openai/types/fine_tuning/__pycache__/job_list_params.cpython-38.pyc,,
openai/types/fine_tuning/fine_tuning_job.py,sha256=0S4vHuMFT40qhiXGAl-MqToAwIF3feYSA9OeJXQ4tqo,3312
openai/types/fine_tuning/fine_tuning_job_event.py,sha256=TRx0UgtvikmYUiRg0vMEu57cNszpin0gsbbrHiPL-jY,341
openai/types/fine_tuning/job_create_params.py,sha256=ly9Js-QGiQoCoVBVhD7oM-uybkjcPK172ozYI2V6fBM,2578
openai/types/fine_tuning/job_list_events_params.py,sha256=tfpo_bsTGW2ZaPzv5eJyr5F38UEUiwE_MJDNttfKLwo,367
openai/types/fine_tuning/job_list_params.py,sha256=Adm5vy-_0qoKP9Ubf_DOoT_pRkyB1YBdbvbAMAP40hw,363
openai/types/image.py,sha256=HXUIin-8Jb643tEtHYg9YAE59-kqXdycB1p4XqS5qAY,574
openai/types/image_create_variation_params.py,sha256=e1zOZaTKg37Qe6LgDt0haqHPdOpqj4KYmzGfS-dVX_0,1342
openai/types/image_edit_params.py,sha256=0y23-ZDZyS0a_a5E2kfVcjH_BJo5XSi0XAnKxwCCQUU,1702
openai/types/image_generate_params.py,sha256=0XlKCfUmXUi8pQKetb_pri-1qV5xRpU6cjmGynmCFJA,2008
openai/types/images_response.py,sha256=3eOiPIDxOSkxEaNrb0HOnHZmPtFYWQtY_xpC-yJN6U0,241
openai/types/model.py,sha256=d_h7gH9_7A2GrRHw61x8RFPEXM3uOlYqh6XVHGuMQ98,499
openai/types/model_deleted.py,sha256=vDZMiixtF903ce9bGixXpus5J1KrHYGEyu_Eb6kO-m8,195
openai/types/moderation.py,sha256=_l1AiEh2KuWOM_gXyaBDDnL2_ybRGdj3MY0KE9H3Mrw,3946
openai/types/moderation_create_params.py,sha256=VbmYPy75IFhWNeZGV_ocuM_m5w2XdL6ogIDLf0gAwos,921
openai/types/moderation_create_response.py,sha256=S2IXOkUwnlEWH8hKpMI8nPpg2NEiLYE3OfTXL892xvA,451
openai/types/shared/__init__.py,sha256=ZElh_qWWutVp2d2fkFakb8sGeqcB6M_GWtC3SYohO8g,202
openai/types/shared/__pycache__/__init__.cpython-38.pyc,,
openai/types/shared/__pycache__/function_definition.cpython-38.pyc,,
openai/types/shared/__pycache__/function_parameters.cpython-38.pyc,,
openai/types/shared/function_definition.py,sha256=lmbpvpu7Et9FoLWE14pxAr4Sy47moXwQzgPZvX2xuJM,1034
openai/types/shared/function_parameters.py,sha256=Xj6wM1hq1Kp6ZsYyNdbjfW9LLfZwJFH7HWuzqLnMu-0,152
openai/types/shared_params/__init__.py,sha256=ZElh_qWWutVp2d2fkFakb8sGeqcB6M_GWtC3SYohO8g,202
openai/types/shared_params/__pycache__/__init__.cpython-38.pyc,,
openai/types/shared_params/__pycache__/function_definition.cpython-38.pyc,,
openai/types/shared_params/__pycache__/function_parameters.cpython-38.pyc,,
openai/types/shared_params/function_definition.py,sha256=-jNW5TaabjauAoVB2Ov5hzVvlsg1fZ7oerhlwqjFarI,1045
openai/types/shared_params/function_parameters.py,sha256=_zsz301xdUydlEuHd3igO6B660FXZb1muELvc0Eq_a8,188
openai/version.py,sha256=cjbXKO8Ut3aiv4YlQnugff7AdC48MpSndcx96q88Yb8,62
