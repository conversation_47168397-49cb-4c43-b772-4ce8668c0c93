"""
Cloud Function Entry Point for Extract Service

This module provides the entry point for the Extract Service when deployed as a Cloud Function.
It handles both Cloud Scheduler triggers and HTTP triggers for testing.
"""

import os
import json
import logging
from datetime import datetime
from typing import Dict, Any

import functions_framework
from flask import Request

# Import the main extraction logic
from main import run_extraction_job

# Import HTTP client for triggering transform service
from http_client import TransformServiceClient, extract_extraction_data

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@functions_framework.cloud_event
def extract_scheduled(cloud_event):
    """
    Cloud Function entry point for scheduled extraction jobs (Cloud Scheduler trigger)
    
    Args:
        cloud_event: Cloud Event from Cloud Scheduler
    """
    try:
        logger.info(f"Extract service triggered by Cloud Scheduler: {cloud_event}")
        
        # Extract job parameters from the cloud event or use defaults
        job_date_str = datetime.now().strftime('%Y-%m-%d')
        job_run_timestamp_str = datetime.now().strftime('%H-%M-%S')
        
        # Run the extraction job
        logger.info(f"Starting extraction job for date: {job_date_str}")
        extraction_result = run_extraction_job(job_date_str, job_run_timestamp_str)
        
        if extraction_result.get('status') == 'success':
            logger.info("Extraction completed successfully, triggering transform service")
            
            # Trigger the transform service
            transform_client = TransformServiceClient()
            
            # Extract relevant data for transform service
            extraction_data = extract_extraction_data(extraction_result)
            
            # Trigger transformation
            transform_data = {
                "job_date_str": job_date_str,
                "job_run_timestamp_str": job_run_timestamp_str,
                "gcs_bucket_name": os.getenv('GCS_BUCKET_NAME', 'default-bucket'),
                **extraction_data
            }
            transform_response = transform_client.trigger_transform(transform_data)
            
            if transform_response.get('status') == 'success':
                logger.info("Transform service triggered successfully")
                return {
                    'status': 'success',
                    'message': 'Extraction completed and transform service triggered',
                    'extraction_result': extraction_result,
                    'transform_response': transform_response
                }
            else:
                logger.error(f"Failed to trigger transform service: {transform_response}")
                return {
                    'status': 'partial_success',
                    'message': 'Extraction completed but transform service trigger failed',
                    'extraction_result': extraction_result,
                    'transform_error': transform_response
                }
        else:
            logger.error(f"Extraction job failed: {extraction_result}")
            return {
                'status': 'error',
                'message': 'Extraction job failed',
                'error': extraction_result
            }
            
    except Exception as e:
        logger.error(f"Unexpected error in extract_scheduled: {str(e)}")
        return {
            'status': 'error',
            'message': f'Unexpected error: {str(e)}'
        }


@functions_framework.http
def extract_http(request: Request):
    """
    HTTP entry point for the Extract Service (for testing and manual triggers)
    
    Args:
        request: Flask Request object
        
    Returns:
        JSON response with extraction results
    """
    try:
        # Handle CORS for testing
        if request.method == 'OPTIONS':
            headers = {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, POST',
                'Access-Control-Allow-Headers': 'Content-Type',
                'Access-Control-Max-Age': '3600'
            }
            return ('', 204, headers)
        
        # Set CORS headers for actual request
        headers = {'Access-Control-Allow-Origin': '*'}
        
        if request.method == 'GET' and request.path == '/health':
            return (json.dumps({
                'status': 'healthy',
                'service': 'extract_service',
                'timestamp': datetime.utcnow().isoformat(),
                'version': '1.0.0'
            }), 200, headers)
        
        # Parse request data
        request_json = request.get_json(silent=True) or {}
        
        # Extract job parameters
        job_date_str = request_json.get('job_date_str', datetime.now().strftime('%Y-%m-%d'))
        job_run_timestamp_str = request_json.get('job_run_timestamp_str', datetime.now().strftime('%H-%M-%S'))
        trigger_transform = request_json.get('trigger_transform', True)
        include_breaking_news = request_json.get('include_breaking_news', True)  # Always include breaking news by default
        
        logger.info(f"HTTP extraction request for date: {job_date_str}")
        
        # Run the extraction job
        extraction_result = run_extraction_job(job_date_str, job_run_timestamp_str, include_breaking_news=include_breaking_news)
        
        response_data = {
            'status': extraction_result.get('status', 'unknown'),
            'extraction_result': extraction_result,
            'job_date': job_date_str,
            'job_timestamp': job_run_timestamp_str
        }
        
        if extraction_result.get('status') == 'success' and trigger_transform:
            logger.info("Extraction completed successfully, triggering transform service")
            
            # Trigger the transform service
            transform_client = TransformServiceClient()
            
            # Extract relevant data for transform service
            extraction_data = extract_extraction_data(extraction_result)
            
            # Trigger transformation
            transform_data = {
                "job_date_str": job_date_str,
                "job_run_timestamp_str": job_run_timestamp_str,
                "gcs_bucket_name": os.getenv('GCS_BUCKET_NAME', 'default-bucket'),
                **extraction_data
            }
            transform_response = transform_client.trigger_transform(transform_data)
            
            response_data['transform_response'] = transform_response
            
            if transform_response.get('status') != 'success':
                response_data['status'] = 'partial_success'
                response_data['message'] = 'Extraction completed but transform service trigger failed'
        
        status_code = 200 if response_data['status'] in ['success', 'partial_success'] else 500
        
        return (json.dumps(response_data, indent=2), status_code, headers)
        
    except Exception as e:
        logger.error(f"Unexpected error in extract_http: {str(e)}")
        error_response = {
            'status': 'error',
            'message': f'Unexpected error: {str(e)}',
            'timestamp': datetime.utcnow().isoformat()
        }
        return (json.dumps(error_response), 500, headers)


# For local testing
if __name__ == "__main__":
    import functions_framework
    
    # Start the Functions Framework server for local testing
    # functions_framework._http_view_func_registry["extract_http"] = extract_http
    
    print("Starting Extract Service locally on port 8080")
    print("Health check: http://localhost:8080/health")
    print("Manual trigger: POST http://localhost:8080/ with JSON payload")
    
    # This would normally be handled by the Functions Framework
    from flask import Flask
    app = Flask(__name__)

    @app.route('/', methods=['GET', 'POST', 'OPTIONS'])
    def extract_endpoint():
        from flask import request
        return extract_http(request)

    @app.route('/health', methods=['GET'])
    def health():
        return {
            'status': 'healthy',
            'service': 'extract_service',
            'timestamp': datetime.utcnow().isoformat(),
            'version': '1.0.0',
            'eventregistry_api_key': 'configured' if 'NEWS_API_KEY' in globals() else 'missing'
        }
    app.run(host='0.0.0.0', port=8080, debug=True) 