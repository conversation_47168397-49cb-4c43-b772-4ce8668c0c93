"""
Extract Service Cloud Function

This module provides the extract service as a Cloud Function with real business logic.
"""

import os
import logging
import json
import sys
import time
from datetime import datetime, timedelta, timezone
# Using REST client instead of grpcio-based storage
sys.path.append('../shared_utils')
try:
    from gcp_rest_client import StorageRestClient
except ImportError:
    StorageRestClient = None
from dotenv import load_dotenv
import argparse
import inspect
import functions_framework
from flask import Request
import pytz

# Import shared utilities
sys.path.append('../shared_utils')
from logging_config import setup_cloud_logging, get_correlation_id_from_request, log_performance_metrics
from health_check import create_extract_service_health_checker
from metrics import get_metrics

# Import the real extraction logic
from fetcher import (
    fetch_articles_for_category,
    extract_domain,
    DEFAULT_TOTAL_PAGES_TO_FETCH,
    DEFAULT_PAGINATION_DELAY_SECONDS
)
# Import the entire config module to iterate over its members
from config import config

# Import breaking news detection
sys.path.append('../breaking_news_service')
try:
    from detector import BreakingNewsDetector
    from models import BreakingNewsDetectionResult
    BREAKING_NEWS_AVAILABLE = True
except ImportError:
    BreakingNewsDetector = None
    BreakingNewsDetectionResult = None
    BREAKING_NEWS_AVAILABLE = False
    print("Breaking news detection not available - continuing without it")

# Import HTTP client for transform service
from http_client import TransformServiceClient

load_dotenv() # For local development with .env file

# --- Setup Enhanced Logging ---
logger = setup_cloud_logging("extract_service")
metrics = get_metrics("extract_service")

# --- Constants & Default Configurations ---
GCS_BASE_PATH = "raw-news"
DEFAULT_TIME_WINDOW_HOURS = 24 # Default time window from original CATEGORY_FETCH_SETTINGS

def get_gcs_bucket_name() -> str:
    gcs_bucket = os.getenv("GCS_BUCKET_NAME", "newsreporter")
    if not gcs_bucket:
        logger.error("GCS_BUCKET_NAME not found in environment variables.")
        raise ValueError("GCS_BUCKET_NAME not found in environment variables.")
    return gcs_bucket

def prepare_source_uri_query(sources_config_dict: dict) -> dict:
    """Prepares the sourceUri part of the query from a config dictionary."""
    all_source_uris = set()
    # The sources_config_dict is expected to be like:
    # {"Main Category Name": {"Sub Category Name": {"Source Name": "URL", ...}, ...}, ...}
    # Or for simpler structures without main categories (e.g. if a source dict has no top-level keys like "General Politics")
    # it could be {"Sub Category Name": {"Source Name": "URL", ...}, ...}
    # The current structure of individual *_SOURCES dicts is {"Sub Category Name": {"Source Name": "URL", ...}}
    # So, sources_config_dict here will be the actual e.g. POLITICS_SOURCES dictionary.
    for subcategory_name, sources in sources_config_dict.items():
        if isinstance(sources, dict): # Ensure 'sources' is a dictionary of source_name: url
            for source_name, url in sources.items():
                domain = extract_domain(url)
                if domain:
                    all_source_uris.add(domain)
                else:
                    logger.warning(f"Could not extract domain for source '{source_name}' with URL '{url}' in subcategory '{subcategory_name}'.")
        else:
            logger.warning(f"Expected a dictionary of sources for subcategory '{subcategory_name}', but got {type(sources)}. Skipping.")

    if not all_source_uris:
        logger.warning("No source URIs extracted from the provided config dictionary.")
        return {}
    return {"sourceUri": {"$or": sorted(list(all_source_uris))}}

def run_extraction_job(job_date_str: str, job_run_timestamp_str: str, include_breaking_news: bool = True, event=None, context=None):
    """
    Main entry point for the Cloud Function/Run to perform news extraction.
    Dynamically iterates through defined categories and countries in config.py.
    `event` and `context` are for GCP Cloud Function triggers, can be ignored for manual runs.
    """
    logger.info(f"Starting News Reporter ETL - Extract Service Job for date: {job_date_str}, run: {job_run_timestamp_str}.")

    try:
        gcs_bucket_name = get_gcs_bucket_name()
        if StorageRestClient:
            storage_client = StorageRestClient()
        else:
            logger.warning("StorageRestClient not available, some features may not work")
            storage_client = None
    except ValueError as e:
        logger.critical(f"Missing critical environment variable: {e}. Aborting job.")
        return {"status": "error", "message": f"Missing critical environment variable: {e}", "results": []}
    except Exception as e:
        logger.critical(f"Failed to initialize GCP clients: {e}. Aborting job.", exc_info=True)
        return {"status": "error", "message": f"Failed to initialize GCP clients: {e}", "results": []}

    categories_to_process = []
    
    # Dynamically discover categories from config.py
    for name, member in inspect.getmembers(config):
        if name.endswith("_SOURCES") and isinstance(member, dict):
            # Derive category_id from variable name (e.g., POLITICS_SOURCES -> politics)
            category_id = name.replace("_SOURCES", "").lower()
            if category_id == "human_interest": # Handle potential direct URI set for human_interest
                 if all(isinstance(v, str) for v in member.values()): # Simple set of URIs
                     hi_query_details = {"sourceUri": {"$or": sorted(list(member.values()))}}
                     categories_to_process.append((category_id, "direct_query", hi_query_details, None))
                     logger.info(f"Discovered Human Interest (direct URIs) category: {category_id}")
                     continue # Skip normal processing for this specific case
                 # If HUMAN_INTEREST_SOURCES is a dict of dicts like others, it will be processed below.

            logger.info(f"Discovered source category: {category_id} from variable {name}")
            categories_to_process.append((category_id, "sources_dict", member, None))
        
        elif name.endswith("_COUNTRY_CONFIG") and isinstance(member, dict):
            country_iso_from_var = name.replace("_COUNTRY_CONFIG", "").lower() # e.g. GHA_COUNTRY_CONFIG -> gha
            category_id = f"country_{country_iso_from_var}"
            country_er_uri = member.get("er_uri")
            
            if country_er_uri:
                country_query_details = {"sourceLocationUri": {"$or": [country_er_uri]}}
                # Pass the country-specific fetch_settings directly
                country_fetch_settings = member.get("fetch_settings", {})
                logger.info(f"Discovered country category: {category_id} from variable {name} with ER URI: {country_er_uri}")
                # Time window override is not set here, will be fetched from CATEGORY_FETCH_SETTINGS
                categories_to_process.append((category_id, "direct_query", country_query_details, country_fetch_settings))
            else:
                logger.warning(f"No 'er_uri' found in {name} for country {country_iso_from_var}. Skipping.")

    all_results = []
    # Ensure CATEGORY_FETCH_SETTINGS is loaded and available from the config module
    if not hasattr(config, 'CATEGORY_FETCH_SETTINGS') or not isinstance(config.CATEGORY_FETCH_SETTINGS, dict):
        logger.critical("CATEGORY_FETCH_SETTINGS not defined or not a dict in config.py. Cannot proceed.")
        return {"status": "error", "message": "CATEGORY_FETCH_SETTINGS not defined or invalid.", "results": []}
    
    category_fetch_settings_map = config.CATEGORY_FETCH_SETTINGS

    for cat_id, config_type, config_data_or_settings, fetch_settings_override in categories_to_process:
        logger.info(f"--- Preparing to fetch for category: {cat_id} ---")
        
        # Get base settings from CATEGORY_FETCH_SETTINGS
        # For country configs, cat_id will be like "country_gha"
        # We need to ensure CATEGORY_FETCH_SETTINGS has entries for these, or provide a default.
        base_category_settings = category_fetch_settings_map.get(cat_id, {})
        if not base_category_settings:
            logger.warning(f"No specific settings in CATEGORY_FETCH_SETTINGS for '{cat_id}'. Using fetcher defaults and base time window.")
            # Provide minimal defaults if not found, or rely on fetcher's internal defaults
            base_category_settings = {
                "time_window_hours": DEFAULT_TIME_WINDOW_HOURS, # Use a module-level default
                "total_pages_to_fetch": DEFAULT_TOTAL_PAGES_TO_FETCH,
                "pagination_delay_seconds": DEFAULT_PAGINATION_DELAY_SECONDS
            }

        # For country configs, fetch_settings_override will contain total_pages and delay
        # For source_dict categories, fetch_settings_override is None
        final_fetch_settings = base_category_settings.copy()
        if isinstance(fetch_settings_override, dict): # This is for country configs
            final_fetch_settings.update(fetch_settings_override)
            # config_data_or_settings here is the query_details for the country
            query_details_for_api = config_data_or_settings 
        elif config_type == "sources_dict":
            # config_data_or_settings is the actual source dictionary (e.g., POLITICS_SOURCES)
            query_details_for_api = prepare_source_uri_query(config_data_or_settings)
        elif config_type == "direct_query": # E.g. Human Interest direct URIs or pre-formed country query
            query_details_for_api = config_data_or_settings

        time_window_hours = final_fetch_settings.get("time_window_hours", DEFAULT_TIME_WINDOW_HOURS)
        total_pages = final_fetch_settings.get("total_pages_to_fetch", DEFAULT_TOTAL_PAGES_TO_FETCH)
        pagination_delay = final_fetch_settings.get("pagination_delay_seconds", DEFAULT_PAGINATION_DELAY_SECONDS)

        try:
            job_start_datetime_for_query = datetime.strptime(job_date_str, '%Y-%m-%d').replace(tzinfo=timezone.utc)
        except ValueError:
            logger.error(f"Invalid date format for job_date_str: {job_date_str}. Expected YYYY-MM-DD. Using current day.")
            job_start_datetime_for_query = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)

        query_end_time = job_start_datetime_for_query + timedelta(days=1) - timedelta(microseconds=1)
        query_start_time = query_end_time - timedelta(hours=time_window_hours)

        # Augment query_details_for_api with fetch parameters for the fetcher function
        # The fetcher expects total_pages_to_fetch and pagination_delay_seconds within its query_config argument
        query_config_for_fetcher = query_details_for_api.copy() # Start with API specific keys like sourceUri or sourceLocationUri
        query_config_for_fetcher["total_pages_to_fetch"] = total_pages
        query_config_for_fetcher["pagination_delay_seconds"] = pagination_delay
        
        # Add sortBy and articlesSortByAsc if they are in final_fetch_settings (e.g. for "trending")
        if final_fetch_settings.get("sortBy"):
             query_config_for_fetcher["sortBy"] = final_fetch_settings.get("sortBy")
        if final_fetch_settings.get("articlesSortByAsc") is not None:
            query_config_for_fetcher["articlesSortByAsc"] = final_fetch_settings.get("articlesSortByAsc")

        if not query_config_for_fetcher or \
           (config_type == "sources_dict" and not query_config_for_fetcher.get("sourceUri")) or \
           (config_type == "direct_query" and not query_config_for_fetcher): # Check if it's empty after setup
            logger.warning(f"Query config for {cat_id} is effectively empty after setup. Skipping fetch.")
            all_results.append({"category": cat_id, "status": "skipped", "message": "Query config empty after setup."})
            continue

        try:
            result = fetch_articles_for_category(
                category_name=cat_id,
                gcs_bucket_name=gcs_bucket_name,
                gcs_base_path=GCS_BASE_PATH,
                query_config=query_config_for_fetcher, # This now includes total_pages and delay
                date_time_start=query_start_time,
                date_time_end=query_end_time,
                job_date_str=job_date_str,
                job_run_timestamp_str=job_run_timestamp_str,
                storage_client=storage_client,
                apply_post_filters=False 
            )
            all_results.append(result)
            logger.info(f"Finished processing category: {cat_id}. Status: {result.get('status') if isinstance(result, dict) else 'unknown'}")
        except Exception as e:
            logger.error(f"Error processing category {cat_id}: {e}", exc_info=True)
            all_results.append({"category": cat_id, "status": "error", "message": str(e)})
    
    logger.info("--- Extract Service Job Summary ---")
    final_status = "success"
    error_count = 0
    success_count = 0
    skipped_count = 0

    for summary_item in all_results:
        logger.info(summary_item)
        if isinstance(summary_item, dict):
            status = summary_item.get("status")
            if status == "error" or status == "error_uploading_to_gcs":
                error_count +=1
            elif status == "success":
                success_count +=1
            elif status == "skipped":
                skipped_count +=1
            
    if error_count > 0 and success_count > 0:
        final_status = "partial_error"
    elif error_count > 0 and success_count == 0:
        final_status = "error"
    elif not all_results or (success_count == 0 and error_count == 0 and skipped_count >0 ): # All skipped or no categories
        final_status = "no_data_processed" if skipped_count > 0 else "no_categories_found"

    logger.info(f"Job totals: Success: {success_count}, Errors: {error_count}, Skipped: {skipped_count}")
    logger.info(f"News Reporter ETL - Extract Service Job Finished with overall status: {final_status}.")
    
    # Collect GCS paths for successful extractions
    extracted_data_paths = []
    for result in all_results:
        if result.get("status") == "success" and result.get("gcs_path"):
            extracted_data_paths.append(result["gcs_path"])

    # Run breaking news detection if enabled
    breaking_news_result = None
    if include_breaking_news and BREAKING_NEWS_AVAILABLE:
        try:
            logger.info("Running breaking news detection...")
            detector = BreakingNewsDetector()
            breaking_news_result = detector.detect_breaking_news(
                hours_back=2,  # Look back 2 hours for breaking news
                save_to_gcs=True,
                bucket_name=gcs_bucket_name
            )
            logger.info(f"Breaking news detection completed: {len(breaking_news_result.breaking_news_articles)} breaking news articles found")

            # Add breaking news paths to extracted data paths
            if hasattr(breaking_news_result, 'gcs_paths'):
                extracted_data_paths.extend(breaking_news_result.gcs_paths)

        except Exception as e:
            logger.error(f"Breaking news detection failed: {e}", exc_info=True)
            # Don't fail the entire job if breaking news detection fails
    elif include_breaking_news and not BREAKING_NEWS_AVAILABLE:
        logger.warning("Breaking news detection requested but not available")

    return {
        "status": final_status,
        "results": all_results,
        "extracted_data_paths": extracted_data_paths,
        "articles_extracted": sum(r.get("articles_fetched_count", 0) for r in all_results if isinstance(r, dict)),
        "categories_processed": len([r for r in all_results if r.get("status") == "success"]),
        "breaking_news_result": breaking_news_result.dict() if breaking_news_result else None
    }

# HTTP endpoint for Cloud Function (moved to cloud_function_main.py)
# This function is deprecated - use cloud_function_main.py for HTTP server functionality
@functions_framework.http
def extract_service_http_handler(request: Request):
    """
    HTTP Cloud Function entry point for extract service
    """
    # Set CORS headers
    headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
    }
    
    if request.method == 'OPTIONS':
        return ('', 204, headers)
    
    try:
        if request.method == 'GET':
            # Enhanced health check
            try:
                if StorageRestClient:
                    storage_client = StorageRestClient()
                    health_checker = create_extract_service_health_checker(storage_client)
                    health_result = health_checker.perform_health_check()
                else:
                    health_result = {
                        "status": "healthy",
                        "service": "extract_service",
                        "message": "Running with REST client (limited functionality)"
                    }

                status_code = 200 if health_result["status"] == "healthy" else 503
                return (health_result, status_code, headers)

            except Exception as e:
                logger.error("Health check failed", error=e)
                return ({
                    "status": "unhealthy",
                    "service": "extract-service",
                    "timestamp": datetime.now().isoformat(),
                    "error": str(e)
                }, 503, headers)
        
        elif request.method == 'POST':
            # Get correlation ID for request tracking
            correlation_id = get_correlation_id_from_request(request)
            logger.correlation_id = correlation_id

            start_time = time.time()

            # request_json is still parsed in case other parameters are present in the payload.
            request_json = request.get_json(silent=True)

            # Always use current EDT (Toronto) date and time
            toronto_tz = pytz.timezone('America/Toronto')
            now_toronto = datetime.now(toronto_tz)

            job_date_str = now_toronto.strftime('%Y-%m-%d')
            job_run_timestamp_str = now_toronto.strftime('%H%M%S_%f')

            logger.info("Starting extraction job",
                       job_date=job_date_str,
                       job_timestamp=job_run_timestamp_str,
                       correlation_id=correlation_id)

            # Run extraction job with metrics
            with metrics.timer("extraction_job"):
                result = run_extraction_job(
                    job_date_str=job_date_str,
                    job_run_timestamp_str=job_run_timestamp_str
                )
            
            # Record extraction metrics
            total_duration = time.time() - start_time
            success = result.get('status') in ['success', 'partial_error']

            if success:
                metrics.record_articles_processed(
                    result.get('total_articles_extracted', 0),
                    "success"
                )
            else:
                metrics.record_articles_processed(0, "error")
                metrics.record_error("extraction_failed", "extraction_job")

            log_performance_metrics(
                logger,
                "extraction_job",
                total_duration,
                success,
                articles_extracted=result.get('total_articles_extracted', 0),
                categories_processed=len(result.get('extracted_data_paths', {}))
            )

            # Trigger transform service if extraction was successful
            if result.get('status') in ['success', 'partial_error'] and result.get('extracted_data_paths'):
                try:
                    with metrics.timer("transform_service_trigger"):
                        transform_service_url = os.getenv('TRANSFORM_SERVICE_URL')
                        if transform_service_url:
                            transform_client = TransformServiceClient(transform_service_url)
                            transform_response = transform_client.trigger_transform({
                                "job_date_str": job_date_str,
                                "job_run_timestamp_str": job_run_timestamp_str,
                                "extracted_data_paths": result['extracted_data_paths'],
                                "extraction_summary": result,
                                "correlation_id": correlation_id
                            })
                            result['transform_service_response'] = transform_response
                            logger.info("Successfully triggered transform service",
                                       correlation_id=correlation_id)
                            metrics.record_api_call("transform_service", 200, 0)
                        else:
                            logger.warning("TRANSFORM_SERVICE_URL not configured, skipping transform service trigger")
                except Exception as e:
                    logger.error("Failed to trigger transform service", error=e, correlation_id=correlation_id)
                    result['transform_service_error'] = str(e)
                    metrics.record_error("transform_service_trigger_failed", "transform_service_trigger")

            status_code = 200 if result.get('status') in ['success', 'partial_error'] else 500
            return (result, status_code, headers)
        
        else:
            return ({
                "status": "error",
                "message": f"Method {request.method} not allowed"
            }, 405, headers)
    
    except Exception as e:
        metrics.record_error("unexpected_error", "extract_service")
        logger.error("Unexpected error in extract service", error=e)
        return ({
            "status": "error",
            "message": f"Internal server error: {str(e)}",
            "timestamp": datetime.now().isoformat()
        }, 500, headers)

# Entry point for Cloud Functions
@functions_framework.http
def extract_service_http(request):
    """Entry point for Cloud Functions HTTP trigger"""
    return extract_service_http_handler(request)

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="Run the News Reporter Extract Service.")
    parser.add_argument("--date", required=True, help="The target date for extraction in YYYY-MM-DD format.")
    parser.add_argument("--timestamp", required=True, help="The specific run timestamp in HHMMSS_ffffff format.")
    args = parser.parse_args()

    logger.info(f"Running Extract Service directly (main.py __main__ block) for date: {args.date}, timestamp: {args.timestamp}")
    
    # Check if CATEGORY_FETCH_SETTINGS is available in the loaded config module
    if not hasattr(config, 'CATEGORY_FETCH_SETTINGS') or not config.CATEGORY_FETCH_SETTINGS:
        logger.warning("CATEGORY_FETCH_SETTINGS not found or empty in config.py. Local __main__ test run might use default page/delay/time_window settings per category if not defined there.")

    job_result = run_extraction_job(job_date_str=args.date, job_run_timestamp_str=args.timestamp)
    
    logger.info(f"Job finished with status: {job_result.get('status')}")
    if job_result.get('status') not in ["success", "partial_error", "no_data_processed"]: # Allow no_data_processed as a non-error
        logger.error("Extraction job did not complete as expected.")