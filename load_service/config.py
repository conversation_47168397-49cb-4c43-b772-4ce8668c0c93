# Configuration for the Load Service

# --- GCP Project Configuration ---
PROJECT_ID = "news-reporter-13104" # Or use os.getenv("GOOGLE_CLOUD_PROJECT")

# --- GCS Configuration ---
GCS_BUCKET_NAME = "newsreporter" # TODO: Configure with your actual GCS bucket name, e.g., os.getenv("GCS_BUCKET_NAME")
TRANSFORMED_DATA_GCS_PREFIX = "transformed-news" # GCS prefix for transformed articles input (API-native enrichment)

# --- Firestore Configuration ---
FIRESTORE_ARTICLE_COLLECTION = "articles"
FIRESTORE_SOURCES_COLLECTION = "sources"
FIRESTORE_CATALOGUE_COLLECTION = "categories"
FIRESTORE_SUBCATEGORIES_COLLECTION = "subcategories"
# SERVICE_ACCOUNT_KEY_PATH = "path/to/your/service-account-key.json" # TODO: Configure if not using Application Default Credentials