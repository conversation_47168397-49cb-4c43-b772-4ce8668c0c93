"""
HTTP Server for Load Service

This module provides HTTP endpoint functionality for the Load Service
to receive requests from the Transform Service and trigger loading jobs.
"""

import os
import json
import logging
import sys
import time
from datetime import datetime
from typing import Dict, Any, Optional

from flask import Flask, request, jsonify
from google.cloud import storage

# Import shared utilities
sys.path.append('../shared_utils')
from logging_config import setup_cloud_logging, get_correlation_id_from_request, log_performance_metrics
from gcp_rest_client import FirestoreRestClient
from metrics import get_metrics

# Import the loading logic
from main import download_jsonl_file_from_gcs, upload_articles_to_firestore, run_load_job

# Import HTTP contracts
try:
    from schemas.http_contracts import (
        TransformToLoadRequest, 
        TransformToLoadResponse,
        ErrorResponse,
        HealthCheckResponse,
        ServiceStatus,
        HTTPConfig
    )
except ImportError:
    # Fallback for when schemas are not available
    from typing import Dict, Any
    
    class ServiceStatus:
        SUCCESS = "success"
        ERROR = "error"
        IN_PROGRESS = "in_progress"

# Setup Enhanced Logging
logger = setup_cloud_logging("load_service")
metrics = get_metrics("load_service")

# Initialize Flask app
app = Flask(__name__)

# Initialize GCS client
storage_client = None
try:
    storage_client = storage.Client()
    logger.info("GCS client initialized for load service HTTP server")
except Exception as e:
    logger.error(f"Failed to initialize GCS client: {e}")

# Initialize Firestore REST client
firestore_db = None
try:
    firestore_db = FirestoreRestClient()
    logger.info("Firestore REST client initialized for load service HTTP server")
except Exception as e:
    logger.error(f"Failed to initialize Firestore REST client: {e}")


@app.route('/health', methods=['GET'])
def health_check():
    """Enhanced health check endpoint"""
    try:
        # Simple health check
        health_result = {
            'status': 'healthy',
            'service': 'load_service',
            'version': '1.0.0',
            'timestamp': datetime.now().isoformat(),
            'components': {
                'gcs_client': 'healthy' if storage_client else 'unhealthy',
                'firestore_client': 'healthy' if firestore_db else 'unhealthy'
            }
        }

        status_code = 200 if health_result["status"] == "healthy" else 503
        return jsonify(health_result), status_code

    except Exception as e:
        logger.error("Health check failed", error=e)
        return jsonify({
            'status': 'unhealthy',
            'service': 'load_service',
            'version': '1.0.0',
            'timestamp': datetime.now().isoformat(),
            'error': str(e)
        }), 503


@app.route('/load', methods=['POST'])
def trigger_load():
    """
    Main endpoint to trigger load job
    Receives request from Transform Service and starts loading
    """
    try:
        # Parse request data
        request_data = request.get_json()
        if not request_data:
            return jsonify({
                'status': ServiceStatus.ERROR,
                'message': 'No JSON data provided',
                'error_code': 'INVALID_REQUEST'
            }), 400
        
        logger.info(f"Received load request: {json.dumps(request_data, indent=2)}")
        
        # Validate required fields
        required_fields = ['job_metadata', 'total_articles_transformed', 'transformed_data_path']
        missing_fields = [field for field in required_fields if field not in request_data]
        if missing_fields:
            return jsonify({
                'status': ServiceStatus.ERROR,
                'message': f'Missing required fields: {missing_fields}',
                'error_code': 'MISSING_FIELDS'
            }), 400
        
        # Extract job metadata
        job_metadata = request_data['job_metadata']
        job_date_str = job_metadata['job_date_str']
        job_run_timestamp_str = job_metadata['job_run_timestamp_str']
        gcs_bucket_name = job_metadata['gcs_bucket_name']
        gcs_base_path = job_metadata.get('gcs_base_path', 'raw-news')
        
        # Extract other fields
        total_articles_transformed = request_data['total_articles_transformed']
        transformed_data_path = request_data['transformed_data_path']
        transform_job_id = request_data.get('transform_job_id')
        
        # Validate clients
        if not storage_client:
            return jsonify({
                'status': ServiceStatus.ERROR,
                'message': 'GCS client not available',
                'error_code': 'GCS_CLIENT_ERROR',
                'job_metadata': job_metadata
            }), 500
            
        if not firestore_db:
            return jsonify({
                'status': ServiceStatus.ERROR,
                'message': 'Firestore client not available',
                'error_code': 'FIRESTORE_CLIENT_ERROR',
                'job_metadata': job_metadata
            }), 500
        
        # Generate load job ID
        load_job_id = f"load_{job_date_str}_{job_run_timestamp_str}"
        
        logger.info(f"Starting load job {load_job_id}")
        
        # Start load job
        try:
            # Download transformed articles from GCS
            logger.info(f"Downloading transformed articles from: {transformed_data_path}")
            
            # Extract the path components for the download function
            # transformed_data_path should be like: "transformed-news/2024-01-15/10-30-45/transformed_articles.jsonl"
            path_parts = transformed_data_path.strip('/').split('/')
            if len(path_parts) >= 3:
                gcs_prefix = path_parts[0]  # e.g., "transformed-news"
                date_str = path_parts[1]    # e.g., "2024-01-15"
                timestamp_str = path_parts[2]  # e.g., "10-30-45"
            else:
                # Fallback to using job metadata
                gcs_prefix = "transformed-news"
                date_str = job_date_str
                timestamp_str = job_run_timestamp_str
            
            articles = download_jsonl_file_from_gcs(
                bucket_name=gcs_bucket_name,
                gcs_prefix=gcs_prefix,
                date_str=date_str,
                timestamp_str=timestamp_str
            )
            
            if not articles:
                return jsonify({
                    'status': ServiceStatus.ERROR,
                    'message': f'No articles found at {transformed_data_path}',
                    'job_metadata': job_metadata,
                    'load_job_id': load_job_id,
                    'error_code': 'NO_ARTICLES_FOUND'
                }), 404
            
            logger.info(f"Downloaded {len(articles)} articles for load job {load_job_id}")
            
            # Upload articles to Firestore
            logger.info(f"Uploading {len(articles)} articles to Firestore")
            upload_articles_to_firestore(articles)
            
            # Prepare success response
            response_data = {
                'status': ServiceStatus.SUCCESS,
                'message': f'Load completed successfully for job {load_job_id}',
                'job_metadata': job_metadata,
                'load_job_id': load_job_id,
                'articles_loaded': len(articles),
                'sources_updated': None,  # Could be calculated if needed
                'catalogue_nodes_updated': None  # Could be calculated if needed
            }
            
            logger.info(f"Load job {load_job_id} completed successfully")
            return jsonify(response_data), 200
                
        except Exception as e:
            logger.error(f"Error during load job {load_job_id}: {e}", exc_info=True)
            
            response_data = {
                'status': ServiceStatus.ERROR,
                'message': f'Load job {load_job_id} failed with error: {str(e)}',
                'job_metadata': job_metadata,
                'load_job_id': load_job_id,
                'error_code': 'LOAD_EXCEPTION',
                'error_details': {'exception': str(e)}
            }
            
            return jsonify(response_data), 500
            
    except Exception as e:
        logger.error(f"Error processing load request: {e}", exc_info=True)
        
        return jsonify({
            'status': ServiceStatus.ERROR,
            'message': f'Failed to process load request: {str(e)}',
            'error_code': 'REQUEST_PROCESSING_ERROR',
            'error_details': {'exception': str(e)}
        }), 500


@app.errorhandler(404)
def not_found(error):
    """Handle 404 errors"""
    return jsonify({
        'status': ServiceStatus.ERROR,
        'message': 'Endpoint not found',
        'error_code': 'ENDPOINT_NOT_FOUND'
    }), 404


@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors"""
    logger.error(f"Internal server error: {error}")
    return jsonify({
        'status': ServiceStatus.ERROR,
        'message': 'Internal server error',
        'error_code': 'INTERNAL_SERVER_ERROR'
    }), 500


@app.route('/load/enriched', methods=['POST'])
def trigger_enriched_load():
    """
    New endpoint for enriched data from enrich service
    """
    try:
        # Parse request data
        request_data = request.get_json()
        if not request_data:
            return jsonify({
                'status': 'error',
                'message': 'No JSON data provided'
            }), 400

        logger.info(f"Received enriched load request: {json.dumps(request_data, indent=2)}")

        # Extract parameters for enriched data
        job_date_str = request_data.get('job_date_str')
        job_run_timestamp_str = request_data.get('job_run_timestamp_str')
        gcs_bucket_name = request_data.get('gcs_bucket_name')
        enriched_data_path = request_data.get('enriched_data_path')
        total_articles_enriched = request_data.get('total_articles_enriched', 0)

        if not all([job_date_str, job_run_timestamp_str, gcs_bucket_name]):
            return jsonify({
                'status': 'error',
                'message': 'Missing required parameters: job_date_str, job_run_timestamp_str, gcs_bucket_name'
            }), 400

        # Validate clients
        if not storage_client:
            return jsonify({
                'status': 'error',
                'message': 'GCS client not available'
            }), 500

        if not firestore_db:
            return jsonify({
                'status': 'error',
                'message': 'Firestore client not available'
            }), 500

        # Generate load job ID
        load_job_id = f"load_enriched_{job_date_str}_{job_run_timestamp_str}"

        logger.info(f"Starting enriched load job {load_job_id}")

        # Run enriched load job (using the updated run_load_job function)
        result = run_load_job(job_date_str, job_run_timestamp_str)

        return jsonify(result), 200 if result.get('status') == 'success' else 500

    except Exception as e:
        logger.error(f"Error processing enriched load request: {e}", exc_info=True)
        return jsonify({
            'status': 'error',
            'message': f'Request processing failed: {str(e)}',
            'timestamp': datetime.now().isoformat()
        }), 500


def create_app():
    """Factory function to create Flask app"""
    return app


def run_server(host='0.0.0.0', port=8081, debug=False):
    """Run the HTTP server"""
    logger.info(f"Starting Load Service HTTP server on {host}:{port}")
    app.run(host=host, port=port, debug=debug)


if __name__ == '__main__':
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(module)s - %(funcName)s - %(message)s'
    )
    
    # Get configuration from environment
    host = os.getenv('LOAD_SERVICE_HOST', '0.0.0.0')
    port = int(os.getenv('LOAD_SERVICE_PORT', 8081))
    debug = os.getenv('LOAD_SERVICE_DEBUG', 'false').lower() == 'true'
    
    run_server(host=host, port=port, debug=debug) 