// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.rpc.context;

import "google/protobuf/struct.proto";

option go_package = "google.golang.org/genproto/googleapis/rpc/context;context";
option java_multiple_files = true;
option java_outer_classname = "AuditContextProto";
option java_package = "com.google.rpc.context";

// `AuditContext` provides information that is needed for audit logging.
message AuditContext {
  // Serialized audit log.
  bytes audit_log = 1;

  // An API request message that is scrubbed based on the method annotation.
  // This field should only be filled if audit_log field is present.
  // Service Control will use this to assemble a complete log for Cloud Audit
  // Logs and Google internal audit logs.
  google.protobuf.Struct scrubbed_request = 2;

  // An API response message that is scrubbed based on the method annotation.
  // This field should only be filled if audit_log field is present.
  // Service Control will use this to assemble a complete log for Cloud Audit
  // Logs and Google internal audit logs.
  google.protobuf.Struct scrubbed_response = 3;

  // Number of scrubbed response items.
  int32 scrubbed_response_item_count = 4;

  // Audit resource name which is scrubbed.
  string target_resource = 5;
}
