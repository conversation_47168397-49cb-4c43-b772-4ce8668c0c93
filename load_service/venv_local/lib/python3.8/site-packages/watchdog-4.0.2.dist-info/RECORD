../../../bin/watchmedo,sha256=e5jkAM4H96CwMRcJRVlb3ynyfpQ7uCt4C3RizVYAsNM,286
_watchdog_fsevents.cpython-38-darwin.so,sha256=prcwLh27qkiI_R5OzD8oaW3G4ahAwx7Rz1DjW3cvMyY,27824
watchdog-4.0.2.dist-info/AUTHORS,sha256=gDi-g3FfUuViWOtmZ_QDsucGBISUxaC2OpsDGD9Evo0,2880
watchdog-4.0.2.dist-info/COPYING,sha256=Ash2D5iKdukqnWy1JUVqhvew_RlThw3Ukd5ZVcuXTUE,625
watchdog-4.0.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
watchdog-4.0.2.dist-info/LICENSE,sha256=z8d0m5b2O9McPEK1xHG_dWgUBT6EfBDz6wA0F7xSPTA,11358
watchdog-4.0.2.dist-info/METADATA,sha256=QRU2iDJ_MeVaA5qz7a0OasOOk8pg7Dizjw9hlzhoCJM,38689
watchdog-4.0.2.dist-info/RECORD,,
watchdog-4.0.2.dist-info/WHEEL,sha256=U9m1yHIec9SRKkhtMVkH6kuJxGYAaP4U0QlQfgSnI40,108
watchdog-4.0.2.dist-info/entry_points.txt,sha256=qt_Oe2U5Zlfz7LNA3PHipn3_1zlfRTp9dk3wTS3Ivb8,66
watchdog-4.0.2.dist-info/top_level.txt,sha256=JYP7SvqSWBmdMDcnBwaMd48Ngr9V0t8UbNvmAQ9LTdc,28
watchdog/__init__.py,sha256=FslnbeodNBt4aVEqkpKGrY0KqsG4hCkvsH-hDLujjQI,651
watchdog/__pycache__/__init__.cpython-38.pyc,,
watchdog/__pycache__/events.cpython-38.pyc,,
watchdog/__pycache__/version.cpython-38.pyc,,
watchdog/__pycache__/watchmedo.cpython-38.pyc,,
watchdog/events.py,sha256=ye2hxsOwZ7ufdTsDifkSkiawW23e1GeBysl9WIJ3O_c,15618
watchdog/observers/__init__.py,sha256=Q_nkDlMjMvZ5-Cb59Mgg2S_tcN6UTnSR-JYfClJ-_i4,3777
watchdog/observers/__pycache__/__init__.cpython-38.pyc,,
watchdog/observers/__pycache__/api.cpython-38.pyc,,
watchdog/observers/__pycache__/fsevents.cpython-38.pyc,,
watchdog/observers/__pycache__/fsevents2.cpython-38.pyc,,
watchdog/observers/__pycache__/inotify.cpython-38.pyc,,
watchdog/observers/__pycache__/inotify_buffer.cpython-38.pyc,,
watchdog/observers/__pycache__/inotify_c.cpython-38.pyc,,
watchdog/observers/__pycache__/kqueue.cpython-38.pyc,,
watchdog/observers/__pycache__/polling.cpython-38.pyc,,
watchdog/observers/__pycache__/read_directory_changes.cpython-38.pyc,,
watchdog/observers/__pycache__/winapi.cpython-38.pyc,,
watchdog/observers/api.py,sha256=2jEGKHJDGXi6_WVldjXM2N2D6tQBGwWrZ-9FbPg1SJs,13188
watchdog/observers/fsevents.py,sha256=wl3CXr22XfpHRxKtajuUo8N_GZmaTmBN15CBdfZPBEM,13817
watchdog/observers/fsevents2.py,sha256=NeNw_ttqcqyU-xApTzvfZRlrBv8OzUs7obH1ByNFKPk,9332
watchdog/observers/inotify.py,sha256=DW7-Vuh0TOqn2EAZBsNTucrIU_-EP88BvM3nPAFA5XU,11308
watchdog/observers/inotify_buffer.py,sha256=bElZQuiWmRtmARhE4480ZMTNd1U05wdz8h3pVVLUbZA,4656
watchdog/observers/inotify_c.py,sha256=hLaE9N4r61INgEIEmy1dhsV98M8ibd1NQbb5UUM7CbU,19274
watchdog/observers/kqueue.py,sha256=kcljp6IjdOWjwqFwdl0v_clODRXqUJjHoLZ2Y4YWez4,23607
watchdog/observers/polling.py,sha256=_Ifz8cjiobzgSBZPA73mGbfVW8bbYxd-IOqgce3jbb4,4893
watchdog/observers/read_directory_changes.py,sha256=QQ7HBuNP1ggUR1Bj1jdYKu64c7ywR-wPJCGkSWuwKZc,4420
watchdog/observers/winapi.py,sha256=CKQTyypZmZlwM0ga6K17DTJTkcL6eN30xPCVgFxbqhU,13422
watchdog/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
watchdog/tricks/__init__.py,sha256=hhW3IBlLlPoG23tvz16PFn_2scha01FoQhsZ36LfYBw,9399
watchdog/tricks/__pycache__/__init__.cpython-38.pyc,,
watchdog/utils/__init__.py,sha256=ojajRMRFg9wWpew7EEQJ9MrpS0sUOhAnU1E_O6apMAU,4419
watchdog/utils/__pycache__/__init__.cpython-38.pyc,,
watchdog/utils/__pycache__/bricks.cpython-38.pyc,,
watchdog/utils/__pycache__/delayed_queue.cpython-38.pyc,,
watchdog/utils/__pycache__/dirsnapshot.cpython-38.pyc,,
watchdog/utils/__pycache__/echo.cpython-38.pyc,,
watchdog/utils/__pycache__/event_debouncer.cpython-38.pyc,,
watchdog/utils/__pycache__/patterns.cpython-38.pyc,,
watchdog/utils/__pycache__/platform.cpython-38.pyc,,
watchdog/utils/__pycache__/process_watcher.cpython-38.pyc,,
watchdog/utils/bricks.py,sha256=XIllJjWk86fAv4sk5HP790L9ylW8U9HwOv533SfuCGw,2883
watchdog/utils/delayed_queue.py,sha256=kKrjo3YGEp7A8akBuD7IUP2RJ5wbBmOUMvWe3M-Tm9g,3076
watchdog/utils/dirsnapshot.py,sha256=LXH5k7V_38OzP4HsOQCShve0nfW8Si5S2Qzkdt1mRdE,15088
watchdog/utils/echo.py,sha256=r0fSIdDrvdVapAa5V-0QKWoO1znrtVAuQDKFtl9Z2pw,5114
watchdog/utils/event_debouncer.py,sha256=Vc9bJMtysfl4ChQGdPWCnqzoCm0h_MR6AYcV-DfncRQ,1797
watchdog/utils/patterns.py,sha256=balydO2crQ57FuLVh2AuuSb2ujB2eeZFErOKZcp6WSM,3158
watchdog/utils/platform.py,sha256=xk1VGgUaTjTWLAJkNw-oIc4oyN0KrTCqeKLjkmA_Y8k,1498
watchdog/utils/process_watcher.py,sha256=1pagFh0zqfezEiuOW9zexlPUhn4IViO3m4zU9DWo8rc,767
watchdog/version.py,sha256=ZOQWai_OBbgcUQxqXqArgBfP_Wyw3CvO66Vm7cZOyDg,1002
watchdog/watchmedo.py,sha256=wbraD-zM4W0iLt6zcENy8nL_xxPb4Lg4z0ZOljxR900,24854
