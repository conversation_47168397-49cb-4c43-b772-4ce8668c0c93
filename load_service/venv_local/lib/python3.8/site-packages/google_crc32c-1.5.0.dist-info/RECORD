google_crc32c-1.5.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
google_crc32c-1.5.0.dist-info/LICENSE,sha256=UbiP0-niTtzc4d0UWk_KLc0SkRQH8AsG36b_YA-XM8E,11358
google_crc32c-1.5.0.dist-info/METADATA,sha256=-HrIgBh3bY8uelcPfd63iS1gIRFxEBuQ-dBHW9UCGi0,2315
google_crc32c-1.5.0.dist-info/RECORD,,
google_crc32c-1.5.0.dist-info/WHEEL,sha256=pXLpj1wTvqlD6RVUEKOuGQGeFX0NOPyDrsC-9xVDmYU,109
google_crc32c-1.5.0.dist-info/top_level.txt,sha256=r7PLPlKjfhMZLqeRsKXIQdIzbe3Frv_2_b8XmcvZ4FQ,14
google_crc32c-1.5.0.dist-info/zip-safe,sha256=AbpHGcgLb-kRsJGnwFEktk7uzpZOCcBY74-YBdrKVGs,1
google_crc32c/.dylibs/libcrc32c.1.1.0.dylib,sha256=w0VzNVUhzxI-V_2B9FQwli3PTG65HGQWo7XQn7GNdrU,101168
google_crc32c/__config__.py,sha256=p-MANcyBrdb_NMqvdzCg334AAbmNfgNvaFkJzzPPwMQ,1293
google_crc32c/__init__.py,sha256=Dun6VhGxQ-8fydGR_hX4U9K3n8XPYXCDKmoIBAatpzM,1291
google_crc32c/__pycache__/__config__.cpython-38.pyc,,
google_crc32c/__pycache__/__init__.cpython-38.pyc,,
google_crc32c/__pycache__/_checksum.cpython-38.pyc,,
google_crc32c/__pycache__/cext.cpython-38.pyc,,
google_crc32c/__pycache__/python.cpython-38.pyc,,
google_crc32c/_checksum.py,sha256=r_2PckK1pwWlW9E6e9xys4qdT4Z7JZe2WfYDFzNQbNI,2636
google_crc32c/_crc32c.cpython-38-darwin.so,sha256=1FDj6tBjITI79Op2N9VAvomtI9WqJizcDMySDblXT7c,52864
google_crc32c/cext.py,sha256=oF7NFpKWUOEoyYlbVGIAJVo8SNDiWRT_VYyCI14-5jo,1557
google_crc32c/python.py,sha256=9zy6ABAB_KM8JJRcZnE4b68VqnOEFRNmCGufrjcb7qc,5661
