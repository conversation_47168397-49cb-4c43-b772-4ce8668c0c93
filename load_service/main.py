# Main script for the Load Service - Cloud Function Version
# Based on original load_service/main.py with Cloud Functions integration

import argparse
import json
import logging
import os
import re
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional, Set, Tuple
import sys
import concurrent.futures
import time

# Cloud Functions imports
from functions_framework import http
import flask

# Google Cloud Storage
from google.cloud import storage

# Import shared utilities
sys.path.append('../shared_utils')
from gcp_rest_client import FirestoreRestClient

from dotenv import load_dotenv

# Configuration from the service's config.py
try:
    from config import (
        PROJECT_ID,
        GCS_BUCKET_NAME,
        TRANSFORMED_DATA_GCS_PREFIX,
        FIRESTORE_ARTICLE_COLLECTION,
        FIRESTORE_SOURCES_COLLECTION,
        FIRESTORE_CATALOGUE_COLLECTION,
        FIRESTORE_SUBCATEGORIES_COLLECTION
    )
except ImportError:
    # Fallback imports for Cloud Functions
    try:
        from .config import (
            PROJECT_ID,
            GCS_BUCKET_NAME,
            TRANSFORMED_DATA_GCS_PREFIX,
            FIRESTORE_ARTICLE_COLLECTION,
            FIRESTORE_SOURCES_COLLECTION,
            FIRESTORE_CATALOGUE_COLLECTION,
            FIRESTORE_SUBCATEGORIES_COLLECTION
        )
    except ImportError:
        # Hard-coded fallback
        PROJECT_ID = "news-reporter-13104"
        GCS_BUCKET_NAME = "newsreporter"
        TRANSFORMED_DATA_GCS_PREFIX = "transformed-news"
        FIRESTORE_ARTICLE_COLLECTION = "articles"
        FIRESTORE_SOURCES_COLLECTION = "sources"
        FIRESTORE_CATALOGUE_COLLECTION = "categories"
        FIRESTORE_SUBCATEGORIES_COLLECTION = "subcategories"

# --- Constants ---
FIRESTORE_BATCH_SIZE = 500 # Max 500 operations per commit

# --- Setup Logging ---
log_format = "%(asctime)s - %(levelname)s - %(module)s - %(funcName)s - %(message)s"
logging.basicConfig(level=logging.INFO, format=log_format)
logger = logging.getLogger(__name__)

# --- Environment Variables & Clients ---
# Load .env from project root if present
script_dir = os.path.dirname(__file__)
project_root = os.path.abspath(os.path.join(script_dir, '..', '..')) # Adjust if service is deeper
dotenv_path = os.path.join(project_root, '.env')
if os.path.exists(dotenv_path):
    load_dotenv(dotenv_path=dotenv_path)
    logger.info(f"Loaded .env file from: {dotenv_path}")
else:
    logger.info(f".env file not found at {dotenv_path}, relying on environment variables set elsewhere.")

# Initialize GCS Client
gcs_client: Optional[storage.Client] = None
try:
    gcs_client = storage.Client(project=PROJECT_ID)
    logger.info(f"GCS client initialized for project: {PROJECT_ID}")
except Exception as e:
    logger.error(f"Error initializing GCS client: {e}", exc_info=True)

# Initialize Firestore REST Client
firestore_db: Optional[FirestoreRestClient] = None
try:
    firestore_db = FirestoreRestClient()
    logger.info("Firestore REST client initialized successfully")

except Exception as e:
    logger.error(f"Error initializing Firestore REST client: {e}", exc_info=True)
    firestore_db = None # Ensure it's None if init fails

# --- Helper Functions ---

def download_jsonl_file_from_gcs(
    bucket_name: str,
    gcs_prefix: str,
    date_str: str,
    timestamp_str: str,
    filename: str
) -> List[Dict[str, Any]]:
    """Downloads a JSONL file from GCS and returns list of parsed objects."""
    if not gcs_client:
        logger.error("GCS client not initialized. Cannot download file.")
        return []

    file_path = f"{gcs_prefix}/{date_str}/{timestamp_str}/{filename}"
    
    logger.info(f"Attempting to download: gs://{bucket_name}/{file_path}")
    
    data_list: List[Dict[str, Any]] = []
    try:
        bucket = gcs_client.bucket(bucket_name)
        blob = bucket.blob(file_path)

        if not blob.exists():
            logger.warning(f"File gs://{bucket_name}/{file_path} does not exist.")
            return []

        jsonl_data_str = blob.download_as_string().decode('utf-8')
        logger.info(f"Downloaded JSONL string. Total length: {len(jsonl_data_str)} characters.")
        
        lines = jsonl_data_str.strip().splitlines()
        
        for line_number, line_content in enumerate(lines, 1):
            if line_content.strip():
                try:
                    data = json.loads(line_content)
                    data_list.append(data)
                except json.JSONDecodeError as e:
                    logger.error(f"Error decoding JSON from line {line_number} in {file_path}: {e}")
                except Exception as e:
                    logger.error(f"Error processing data from line {line_number} in {file_path}: {e}")
            else:
                logger.debug(f"Skipping empty or whitespace-only line at original index {line_number -1} after split.")
            
    except Exception as e:
        logger.error(f"Error downloading or processing file gs://{bucket_name}/{file_path}: {e}", exc_info=True)
        return []
        
    logger.info(f"Successfully downloaded and parsed {len(data_list)} items from: gs://{bucket_name}/{file_path}")
    return data_list

def ensure_firestore_collections_exist():
    """Ensure all required Firestore collections exist, create if they don't."""
    if not firestore_db:
        logger.warning("Firestore not available, skipping collection existence check")
        return

    collections_to_check = [
        FIRESTORE_ARTICLE_COLLECTION,
        FIRESTORE_SOURCES_COLLECTION,
        FIRESTORE_CATALOGUE_COLLECTION,
        FIRESTORE_SUBCATEGORIES_COLLECTION
    ]

    logger.info("Checking Firestore collection existence...")

    for collection_name in collections_to_check:
        try:
            # Try to get a single document from the collection to check if it exists
            collection_ref = firestore_db.collection(collection_name)
            # This will create the collection if it doesn't exist when we write to it
            logger.info(f"✅ Collection '{collection_name}' is accessible")
        except Exception as e:
            logger.error(f"❌ Error accessing collection '{collection_name}': {e}")
            # Collections are created automatically when first document is written
            # So we just log the error but don't fail

    logger.info("Firestore collection existence check completed")

def upload_subcategories_to_firestore(subcategories: List[Dict[str, Any]]):
    """Upload subcategories with smart deduplication and update logic."""
    if not firestore_db or not subcategories:
        logger.info("No subcategories to upload or Firestore not available.")
        return

    collection_ref = firestore_db.collection(FIRESTORE_SUBCATEGORIES_COLLECTION)
    logger.info(f"Starting upload of {len(subcategories)} subcategories with deduplication.")

    batch = firestore_db.batch()
    batch_count = 0
    processed_count = 0
    updated_count = 0
    new_count = 0

    for subcategory in subcategories:
        try:
            subcategory_id = subcategory.get('subcategory_id')
            if not subcategory_id:
                logger.warning("Subcategory missing subcategory_id, skipping")
                continue

            doc_ref = collection_ref.document(subcategory_id)

            # Get existing document to check for updates
            existing_doc = doc_ref.get()

            # Prepare subcategory data with timestamp conversion
            subcategory_data = subcategory.copy()
            for key, value in subcategory_data.items():
                if isinstance(value, str) and ('_at' in key or key.endswith('_time')):
                    try:
                        subcategory_data[key] = datetime.fromisoformat(value.replace('Z', '+00:00'))
                    except (ValueError, AttributeError):
                        pass

            # Add processing metadata
            subcategory_data['last_updated'] = datetime.now(timezone.utc)
            subcategory_data['load_timestamp'] = datetime.now(timezone.utc)

            if existing_doc.exists:
                # Subcategory exists - check if update is needed
                existing_data = existing_doc.to_dict()

                # Compare key fields to determine if update is needed
                needs_update = False
                update_fields = ['name', 'category_name', 'article_count', 'topic_count', 'topics']

                for field in update_fields:
                    if subcategory_data.get(field) != existing_data.get(field):
                        needs_update = True
                        break

                if needs_update:
                    batch.update(doc_ref, subcategory_data)
                    updated_count += 1
                    logger.debug(f"Updated subcategory: {subcategory_id}")
                else:
                    logger.debug(f"Subcategory unchanged, skipping: {subcategory_id}")
                    continue
            else:
                # New subcategory
                batch.set(doc_ref, subcategory_data)
                new_count += 1
                logger.debug(f"New subcategory: {subcategory_id}")

            batch_count += 1
            processed_count += 1

            # Commit batch when it reaches the limit
            if batch_count >= FIRESTORE_BATCH_SIZE or processed_count == len(subcategories):
                logger.info(f"Committing batch of {batch_count} subcategories... ({processed_count}/{len(subcategories)})")
                try:
                    batch.commit()
                    logger.info(f"Successfully committed batch of {batch_count} subcategories.")
                except Exception as e:
                    logger.error(f"Failed to commit subcategories batch: {e}")
                    raise

                # Reset batch for next iteration
                batch = firestore_db.batch()
                batch_count = 0

        except Exception as e:
            logger.error(f"Error processing subcategory {subcategory.get('subcategory_id', 'unknown')}: {e}")
            continue

    logger.info(f"Subcategories upload completed: {processed_count} processed, {new_count} new, {updated_count} updated")

def upload_articles_to_firestore(articles: List[Dict[str, Any]]):
    """Upload articles with smart deduplication and update logic."""
    if not firestore_db or not articles:
        logger.info("No articles to upload or Firestore not available.")
        return

    collection_ref = firestore_db.collection(FIRESTORE_ARTICLE_COLLECTION)
    logger.info(f"Starting upload of {len(articles)} articles with deduplication.")

    batch = firestore_db.batch()
    batch_count = 0
    processed_count = 0
    updated_count = 0
    new_count = 0

    for article in articles:
        try:
            uri = article.get('uri')
            if not uri:
                logger.warning("Article missing URI, skipping")
                continue

            doc_ref = collection_ref.document(uri)
            
            # Get existing document to check for updates
            existing_doc = doc_ref.get()
            
            # Prepare article data with timestamp conversion
            article_data = article.copy()
            for key, value in article_data.items():
                if isinstance(value, str) and ('_at' in key or key.endswith('_time')):
                    try:
                        article_data[key] = datetime.fromisoformat(value.replace('Z', '+00:00'))
                    except (ValueError, AttributeError):
                        pass

            # Add processing metadata
            article_data['last_updated'] = datetime.now(timezone.utc)
            article_data['load_timestamp'] = datetime.now(timezone.utc)

            if existing_doc.exists:
                # Article exists - check if update is needed
                existing_data = existing_doc.to_dict()
                
                # Compare key fields to determine if update is needed
                needs_update = False
                update_fields = ['title', 'body', 'custom_category_id', 'custom_subcategory_id',
                               'custom_topic_names', 'sentiment_score', 'trending_score',
                               'key_takeaways', 'reading_time_minutes', 'summary',
                               'custom_category_name', 'custom_subcategory_name']
                
                for field in update_fields:
                    if article_data.get(field) != existing_data.get(field):
                        needs_update = True
                        break
                
                if needs_update:
                    # Merge with existing data, preserving important fields
                    merged_data = existing_data.copy()
                    merged_data.update(article_data)
                    
                    # Preserve creation timestamp
                    if 'created_at' in existing_data:
                        merged_data['created_at'] = existing_data['created_at']
                    
                    batch.set(doc_ref, merged_data)
                    updated_count += 1
                else:
                    # No update needed, just update the load timestamp
                    batch.update(doc_ref, {
                        'last_updated': datetime.now(timezone.utc),
                        'load_timestamp': datetime.now(timezone.utc)
                    })
            else:
                # New article
                article_data['created_at'] = datetime.now(timezone.utc)
                batch.set(doc_ref, article_data)
                new_count += 1

            batch_count += 1
            processed_count += 1

            # Commit batch when it reaches the limit
            if batch_count >= FIRESTORE_BATCH_SIZE or processed_count == len(articles):
                logger.info(f"Committing batch of {batch_count} articles... ({processed_count}/{len(articles)})")
                try:
                    batch.commit()
                    logger.info(f"Successfully committed batch of {batch_count} articles.")
                except Exception as e:
                    logger.error(f"Error committing article batch: {e}", exc_info=True)
                
                batch = firestore_db.batch()
                batch_count = 0

        except Exception as e:
            logger.error(f"Error processing article {article.get('uri', 'unknown')}: {e}", exc_info=True)
            processed_count += 1

    logger.info(f"Articles upload complete: {new_count} new, {updated_count} updated, {processed_count} total processed.")


def upload_catalogue_to_firestore(catalogue_items: List[Dict[str, Any]]):
    """Upload catalogue with smart topic merging and trending updates."""
    if not firestore_db or not catalogue_items:
        logger.info("No catalogue items to upload or Firestore not available.")
        return

    collection_ref = firestore_db.collection(FIRESTORE_CATALOGUE_COLLECTION)
    logger.info(f"Starting upload of {len(catalogue_items)} catalogue items with smart merging.")

    batch = firestore_db.batch()
    batch_count = 0
    processed_count = 0
    updated_count = 0
    new_count = 0

    for catalogue_item in catalogue_items:
        try:
            category_id = catalogue_item.get('category_id')
            if not category_id:
                logger.warning("Catalogue item missing category_id, skipping")
                continue

            doc_ref = collection_ref.document(category_id)
            existing_doc = doc_ref.get()

            # Prepare catalogue data
            catalogue_data = catalogue_item.copy()
            catalogue_data['last_updated'] = datetime.now(timezone.utc)
            catalogue_data['load_timestamp'] = datetime.now(timezone.utc)

            if existing_doc.exists:
                # Merge with existing catalogue data
                existing_data = existing_doc.to_dict()
                
                # Smart merge topics by type
                merged_topics = {}
                existing_topics = existing_data.get('topics', {})
                new_topics = catalogue_data.get('topics', {})
                
                for topic_type in ['concept', 'organization', 'person', 'location']:
                    existing_type_topics = existing_topics.get(topic_type, [])
                    new_type_topics = new_topics.get(topic_type, [])
                    
                    # Create lookup for existing topics by keyword
                    existing_lookup = {topic.get('keyword'): topic for topic in existing_type_topics}
                    
                    # Merge topics, updating existing and adding new
                    merged_type_topics = []
                    
                    # Add/update from new topics
                    for new_topic in new_type_topics:
                        keyword = new_topic.get('keyword')
                        if keyword in existing_lookup:
                            # Update existing topic with new data
                            existing_topic = existing_lookup[keyword]
                            merged_topic = existing_topic.copy()
                            
                            # Update trending data and scores
                            merged_topic.update({
                                'trending_level': new_topic.get('trending_level', existing_topic.get('trending_level')),
                                'trending_score': max(new_topic.get('trending_score', 0), existing_topic.get('trending_score', 0)),
                                'eventregistry_score': max(new_topic.get('eventregistry_score', 0), existing_topic.get('eventregistry_score', 0)),
                                'article_count': new_topic.get('article_count', existing_topic.get('article_count', 0)),
                                'last_seen': new_topic.get('last_seen', existing_topic.get('last_seen'))
                            })
                            
                            # Merge search terms
                            existing_terms = set(existing_topic.get('search_terms', []))
                            new_terms = set(new_topic.get('search_terms', []))
                            merged_topic['search_terms'] = list(existing_terms.union(new_terms))
                            
                            merged_type_topics.append(merged_topic)
                            del existing_lookup[keyword]  # Mark as processed
                        else:
                            # New topic
                            merged_type_topics.append(new_topic)
                    
                    # Add remaining existing topics that weren't updated
                    for remaining_topic in existing_lookup.values():
                        merged_type_topics.append(remaining_topic)
                    
                    # Limit to top 5 topics by trending_score for each type
                    sorted_topics = sorted(merged_type_topics, 
                                         key=lambda x: x.get('trending_score', 0), 
                                         reverse=True)
                    original_count = len(sorted_topics)
                    limited_topics = sorted_topics[:5]  # Keep only top 5
                    merged_topics[topic_type] = limited_topics
                    
                    if original_count > 5:
                        logger.info(f"Limited {topic_type} topics from {original_count} to {len(limited_topics)} for category {category_id}")

                # Update catalogue data with merged topics
                merged_data = existing_data.copy()
                merged_data.update(catalogue_data)
                merged_data['topics'] = merged_topics
                
                # Recalculate aggregated stats
                total_topics = sum(len(topics) for topics in merged_topics.values())
                trending_topics = sum(1 for topics in merged_topics.values() 
                                    for topic in topics 
                                    if topic.get('trending_level') in ['high', 'medium'])
                
                merged_data.update({
                    'total_topics': total_topics,
                    'trending_topics_count': trending_topics
                })
                
                # Preserve creation timestamp
                if 'created_at' in existing_data:
                    merged_data['created_at'] = existing_data['created_at']
                
                batch.set(doc_ref, merged_data)
                updated_count += 1
            else:
                # New catalogue item - limit to top 5 topics per type
                topics = catalogue_data.get('topics', {})
                limited_topics = {}
                
                for topic_type in ['concept', 'organization', 'person', 'location']:
                    type_topics = topics.get(topic_type, [])
                    if type_topics:
                        # Sort by trending_score and limit to top 5
                        sorted_topics = sorted(type_topics, 
                                             key=lambda x: x.get('trending_score', 0), 
                                             reverse=True)
                        original_count = len(sorted_topics)
                        limited_topics[topic_type] = sorted_topics[:5]
                        
                        if original_count > 5:
                            logger.info(f"Limited {topic_type} topics from {original_count} to 5 for new category {category_id}")
                    else:
                        limited_topics[topic_type] = []
                
                # Update catalogue data with limited topics
                catalogue_data['topics'] = limited_topics
                
                # Recalculate stats based on limited topics
                total_topics = sum(len(topics) for topics in limited_topics.values())
                trending_topics = sum(1 for topics in limited_topics.values() 
                                    for topic in topics 
                                    if topic.get('trending_level') in ['high', 'medium'])
                
                catalogue_data.update({
                    'total_topics': total_topics,
                    'trending_topics_count': trending_topics
                })
                
                catalogue_data['created_at'] = datetime.now(timezone.utc)
                batch.set(doc_ref, catalogue_data)
                new_count += 1

            batch_count += 1
            processed_count += 1

            # Commit batch when it reaches the limit
            if batch_count >= FIRESTORE_BATCH_SIZE or processed_count == len(catalogue_items):
                logger.info(f"Committing batch of {batch_count} catalogue items... ({processed_count}/{len(catalogue_items)})")
                try:
                    batch.commit()
                    logger.info(f"Successfully committed batch of {batch_count} catalogue items.")
                except Exception as e:
                    logger.error(f"Error committing catalogue batch: {e}", exc_info=True)
                
                batch = firestore_db.batch()
                batch_count = 0

        except Exception as e:
            logger.error(f"Error processing catalogue item {catalogue_item.get('category_id', 'unknown')}: {e}", exc_info=True)
            processed_count += 1

    logger.info(f"Catalogue upload complete: {new_count} new, {updated_count} updated, {processed_count} total processed.")


def upload_sources_to_firestore(sources: List[Dict[str, Any]]):
    """Upload sources with incremental stats updates."""
    if not firestore_db or not sources:
        logger.info("No sources to upload or Firestore not available.")
        return

    collection_ref = firestore_db.collection(FIRESTORE_SOURCES_COLLECTION)
    logger.info(f"Starting upload of {len(sources)} sources with incremental updates.")

    batch = firestore_db.batch()
    batch_count = 0
    processed_count = 0
    updated_count = 0
    new_count = 0

    for source in sources:
        try:
            # Use source_id or uri as document ID
            source_id = source.get('source_id') or source.get('uri') or source.get('id')
            if not source_id:
                logger.warning("Source missing identifier, skipping")
                continue

            doc_ref = collection_ref.document(str(source_id))
            existing_doc = doc_ref.get()

            # Prepare source data
            source_data = source.copy()
            source_data['last_updated'] = datetime.now(timezone.utc)
            source_data['load_timestamp'] = datetime.now(timezone.utc)

            if existing_doc.exists:
                # Update existing source with incremental stats
                existing_data = existing_doc.to_dict()
                merged_data = existing_data.copy()
                
                # Update stats incrementally
                merged_data.update({
                    'article_count': source_data.get('article_count', existing_data.get('article_count', 0)),
                    'last_article_date': source_data.get('last_article_date', existing_data.get('last_article_date')),
                    'categories_covered': source_data.get('categories_covered', existing_data.get('categories_covered', [])),
                    'avg_sentiment': source_data.get('avg_sentiment', existing_data.get('avg_sentiment')),
                    'trending_topics_count': source_data.get('trending_topics_count', existing_data.get('trending_topics_count', 0)),
                    'last_updated': datetime.now(timezone.utc),
                    'load_timestamp': datetime.now(timezone.utc)
                })
                
                # Merge category lists
                existing_categories = set(existing_data.get('categories_covered', []))
                new_categories = set(source_data.get('categories_covered', []))
                merged_data['categories_covered'] = list(existing_categories.union(new_categories))
                
                # Preserve creation timestamp
                if 'created_at' in existing_data:
                    merged_data['created_at'] = existing_data['created_at']
                
                batch.set(doc_ref, merged_data)
                updated_count += 1
            else:
                # New source
                source_data['created_at'] = datetime.now(timezone.utc)
                batch.set(doc_ref, source_data)
                new_count += 1

            batch_count += 1
            processed_count += 1

            # Commit batch when it reaches the limit
            if batch_count >= FIRESTORE_BATCH_SIZE or processed_count == len(sources):
                logger.info(f"Committing batch of {batch_count} sources... ({processed_count}/{len(sources)})")
                try:
                    batch.commit()
                    logger.info(f"Successfully committed batch of {batch_count} sources.")
                except Exception as e:
                    logger.error(f"Error committing sources batch: {e}", exc_info=True)
                
                batch = firestore_db.batch()
                batch_count = 0

        except Exception as e:
            logger.error(f"Error processing source {source.get('source_id', 'unknown')}: {e}", exc_info=True)
            processed_count += 1

    logger.info(f"Sources upload complete: {new_count} new, {updated_count} updated, {processed_count} total processed.")


def process_category_topics(category_id: str, category_topics: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Process topics for a single category in parallel."""
    logger.info(f"Processing {len(category_topics)} topics for category: {category_id}")
    
    topics_new = 0
    topics_updated = 0
    topics_processed = 0
    
    try:
        # Get reference to the topics subcollection
        topics_collection = firestore_db.collection(FIRESTORE_CATALOGUE_COLLECTION).document(category_id).collection('topics')
        topic_batch = firestore_db.batch()
        topic_batch_count = 0

        for topic in category_topics:
            try:
                topic_id = topic.get('id')
                if not topic_id:
                    logger.warning("Topic missing id, skipping")
                    continue

                doc_ref = topics_collection.document(topic_id)
                existing_doc = doc_ref.get()

                # Prepare topic data
                topic_data = topic.copy()
                topic_data['last_updated'] = datetime.now(timezone.utc)
                topic_data['load_timestamp'] = datetime.now(timezone.utc)

                if existing_doc.exists:
                    # Update existing topic
                    existing_data = existing_doc.to_dict()
                    merged_data = existing_data.copy()
                    merged_data.update(topic_data)
                    
                    # Keep highest trending score and article count
                    if existing_data.get('trendingScore', 0) > topic_data.get('trendingScore', 0):
                        merged_data['trendingScore'] = existing_data['trendingScore']
                        merged_data['trendingLevel'] = existing_data.get('trendingLevel', topic_data.get('trendingLevel'))
                    
                    merged_data['articleCount'] = merged_data.get('articleCount', 0) + topic_data.get('articleCount', 0)
                    
                    # Merge aliases
                    existing_aliases = set(existing_data.get('aliases', []))
                    new_aliases = set(topic_data.get('aliases', []))
                    if existing_aliases or new_aliases:
                        merged_data['aliases'] = list(existing_aliases.union(new_aliases))
                    
                    # Preserve creation timestamp
                    if 'created_at' in existing_data:
                        merged_data['created_at'] = existing_data['created_at']
                    elif 'createdAt' in existing_data:
                        merged_data['createdAt'] = existing_data['createdAt']
                    
                    topic_batch.set(doc_ref, merged_data)
                    topics_updated += 1
                else:
                    # New topic
                    if 'created_at' not in topic_data and 'createdAt' not in topic_data:
                        topic_data['created_at'] = datetime.now(timezone.utc)
                    topic_batch.set(doc_ref, topic_data)
                    topics_new += 1

                topic_batch_count += 1
                topics_processed += 1

                # Commit batch when it reaches the limit
                if topic_batch_count >= FIRESTORE_BATCH_SIZE or topics_processed == len(category_topics):
                    logger.info(f"Committing batch of {topic_batch_count} topics for category {category_id}... ({topics_processed}/{len(category_topics)})")
                    try:
                        topic_batch.commit()
                        logger.info(f"Successfully committed batch of {topic_batch_count} topics for category {category_id}.")
                        # Add small delay to reduce Firestore pressure
                        time.sleep(0.1)
                    except Exception as e:
                        logger.error(f"Error committing topic batch for category {category_id}: {e}", exc_info=True)
                    
                    topic_batch = firestore_db.batch()
                    topic_batch_count = 0

            except Exception as e:
                logger.error(f"Error processing topic {topic.get('id', 'unknown')} in category {category_id}: {e}", exc_info=True)
                topics_processed += 1

    except Exception as e:
        logger.error(f"Error processing topics for category {category_id}: {e}", exc_info=True)
        
    return {
        'category_id': category_id,
        'topics_new': topics_new,
        'topics_updated': topics_updated,
        'topics_processed': topics_processed
    }


def upload_categories_and_topics_to_firestore(categories: List[Dict[str, Any]], topics: List[Dict[str, Any]]):
    """Upload categories and topics using Firestore subcollection structure with parallel processing."""
    if not firestore_db:
        logger.info("Firestore not available for categories and topics upload.")
        return

    if not categories and not topics:
        logger.info("No categories or topics to upload.")
        return

    logger.info(f"Starting upload of {len(categories)} categories and {len(topics)} topics using subcollection structure with parallel processing.")

    # Upload categories first
    categories_processed = 0
    categories_new = 0
    categories_updated = 0

    if categories:
        categories_collection = firestore_db.collection(FIRESTORE_CATALOGUE_COLLECTION)
        category_batch = firestore_db.batch()
        category_batch_count = 0

        for category in categories:
            try:
                category_id = category.get('id')
                if not category_id:
                    logger.warning("Category missing id, skipping")
                    continue

                doc_ref = categories_collection.document(category_id)
                existing_doc = doc_ref.get()

                # Prepare category data
                category_data = category.copy()
                category_data['last_updated'] = datetime.now(timezone.utc)
                category_data['load_timestamp'] = datetime.now(timezone.utc)

                if existing_doc.exists:
                    # Update existing category
                    existing_data = existing_doc.to_dict()
                    merged_data = existing_data.copy()
                    merged_data.update(category_data)
                    
                    # Preserve creation timestamp
                    if 'created_at' in existing_data:
                        merged_data['created_at'] = existing_data['created_at']
                    elif 'createdAt' in existing_data:
                        merged_data['createdAt'] = existing_data['createdAt']
                    
                    category_batch.set(doc_ref, merged_data)
                    categories_updated += 1
                else:
                    # New category
                    if 'created_at' not in category_data and 'createdAt' not in category_data:
                        category_data['created_at'] = datetime.now(timezone.utc)
                    category_batch.set(doc_ref, category_data)
                    categories_new += 1

                category_batch_count += 1
                categories_processed += 1

                # Commit batch when it reaches the limit
                if category_batch_count >= FIRESTORE_BATCH_SIZE or categories_processed == len(categories):
                    logger.info(f"Committing batch of {category_batch_count} categories... ({categories_processed}/{len(categories)})")
                    try:
                        category_batch.commit()
                        logger.info(f"Successfully committed batch of {category_batch_count} categories.")
                    except Exception as e:
                        logger.error(f"Error committing category batch: {e}", exc_info=True)
                    
                    category_batch = firestore_db.batch()
                    category_batch_count = 0

            except Exception as e:
                logger.error(f"Error processing category {category.get('id', 'unknown')}: {e}", exc_info=True)
                categories_processed += 1

    logger.info(f"Categories upload complete: {categories_new} new, {categories_updated} updated, {categories_processed} total processed.")

    # Upload topics to subcollections using parallel processing
    topics_processed = 0
    topics_new = 0
    topics_updated = 0

    if topics:
        # Group topics by category for efficient processing
        topics_by_category = {}
        for topic in topics:
            category_id = topic.get('categoryId')
            if not category_id:
                logger.warning("Topic missing categoryId, skipping")
                continue
            
            if category_id not in topics_by_category:
                topics_by_category[category_id] = []
            topics_by_category[category_id].append(topic)

        logger.info(f"Processing topics for {len(topics_by_category)} categories in parallel...")

        # Process categories in parallel (max 3 workers to avoid overwhelming Firestore)
        with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
            future_to_category = {
                executor.submit(process_category_topics, category_id, category_topics): category_id
                for category_id, category_topics in topics_by_category.items()
            }
            
            for future in concurrent.futures.as_completed(future_to_category):
                category_id = future_to_category[future]
                try:
                    result = future.result()
                    topics_new += result['topics_new']
                    topics_updated += result['topics_updated']
                    topics_processed += result['topics_processed']
                    logger.info(f"Completed processing {result['topics_processed']} topics for category {category_id}")
                except Exception as e:
                    logger.error(f"Error in parallel processing for category {category_id}: {e}", exc_info=True)

    logger.info(f"Topics upload complete: {topics_new} new, {topics_updated} updated, {topics_processed} total processed.")
    logger.info(f"Subcollection upload summary: {categories_processed} categories, {topics_processed} topics processed.")


def upload_category_tree_to_firestore(category_trees: List[Dict[str, Any]]):
    """Upload hierarchical category tree documents to Firestore."""
    if not firestore_db or not category_trees:
        logger.info("No category trees to upload or Firestore not available.")
        return

    collection_ref = firestore_db.collection("category_tree")
    logger.info(f"Starting upload of {len(category_trees)} category tree documents.")

    batch = firestore_db.batch()
    batch_count = 0
    processed_count = 0
    updated_count = 0
    new_count = 0

    for tree in category_trees:
        try:
            # Use category_id as document ID
            category_id = tree.get('category_id')
            if not category_id:
                logger.warning("Category tree missing category_id, skipping")
                continue

            doc_ref = collection_ref.document(str(category_id))
            existing_doc = doc_ref.get()

            # Prepare tree data
            tree_data = tree.copy()
            tree_data['last_updated'] = datetime.now(timezone.utc)
            tree_data['load_timestamp'] = datetime.now(timezone.utc)

            if existing_doc.exists:
                # Update existing tree
                existing_data = existing_doc.to_dict()
                merged_data = existing_data.copy()

                # Update with new data
                merged_data.update({
                    'category_name': tree_data.get('category_name'),
                    'article_count': tree_data.get('article_count', 0),
                    'subcategories': tree_data.get('subcategories', []),
                    'total_subcategories': tree_data.get('total_subcategories', 0),
                    'total_topics': tree_data.get('total_topics', 0),
                    'last_updated': datetime.now(timezone.utc),
                    'load_timestamp': datetime.now(timezone.utc)
                })

                # Preserve creation timestamp
                if 'created_at' in existing_data:
                    merged_data['created_at'] = existing_data['created_at']

                batch.set(doc_ref, merged_data)
                updated_count += 1
            else:
                # New tree
                tree_data['created_at'] = datetime.now(timezone.utc)
                batch.set(doc_ref, tree_data)
                new_count += 1

            batch_count += 1
            processed_count += 1

            # Commit batch when it reaches the limit
            if batch_count >= FIRESTORE_BATCH_SIZE or processed_count == len(category_trees):
                logger.info(f"Committing batch of {batch_count} category tree items... ({processed_count}/{len(category_trees)})")
                try:
                    batch.commit()
                    logger.info(f"Successfully committed batch of {batch_count} category tree items.")
                except Exception as e:
                    logger.error(f"Error committing category tree batch: {e}", exc_info=True)

                batch = firestore_db.batch()
                batch_count = 0

        except Exception as e:
            logger.error(f"Error processing category tree {tree.get('category_id', 'unknown')}: {e}", exc_info=True)
            processed_count += 1

    logger.info(f"Category tree upload complete: {new_count} new, {updated_count} updated, {processed_count} total processed.")


def trigger_embed_service(gcs_bucket: str, job_date: str, job_timestamp: str, article_count: int) -> Dict[str, Any]:
    """Trigger embed service after successful load"""
    import os
    import requests

    embed_service_url = os.getenv("EMBED_SERVICE_URL", "https://embed-service-429922701640.us-central1.run.app")
    embed_service_timeout = int(os.getenv("EMBED_SERVICE_TIMEOUT", "300"))  # 5 minutes

    payload = {
        'gcs_location': f"gs://{gcs_bucket}/enriched-news/{job_date}/{job_timestamp}/",
        'batch_id': f"batch_{job_date}_{job_timestamp}",
        'article_count': article_count
    }

    try:
        logger.info(f"Triggering embed service at {embed_service_url}/embed")
        logger.info(f"Embed payload: {json.dumps(payload, indent=2)}")

        response = requests.post(
            f"{embed_service_url}/embed",
            json=payload,
            timeout=embed_service_timeout,
            headers={'Content-Type': 'application/json'}
        )

        if response.status_code == 200:
            response_data = response.json()
            logger.info(f"✅ Embed service triggered successfully: {response_data.get('message', 'No message')}")
            return {
                'status': 'success',
                'job_id': response_data.get('job_id'),
                'message': response_data.get('message'),
                'response': response_data
            }
        else:
            error_msg = f"Embed service returned HTTP {response.status_code}"
            try:
                error_data = response.json()
                error_msg += f": {error_data.get('message', 'Unknown error')}"
            except:
                error_msg += f": {response.text[:200]}"

            logger.error(f"❌ Embed service trigger failed: {error_msg}")
            return {
                'status': 'error',
                'error': error_msg,
                'http_status': response.status_code
            }

    except requests.exceptions.Timeout:
        error_msg = f"Embed service timeout after {embed_service_timeout} seconds"
        logger.error(f"❌ {error_msg}")
        return {
            'status': 'error',
            'error': error_msg
        }
    except requests.exceptions.RequestException as e:
        error_msg = f"Embed service request failed: {str(e)}"
        logger.error(f"❌ {error_msg}")
        return {
            'status': 'error',
            'error': error_msg
        }


def run_load_job(date_str: str, timestamp_str: str) -> Dict[str, Any]:
    """
    Run the load job - downloads all three transformed files and uploads to Firestore
    with proper deduplication and update logic.
    
    Args:
        date_str: Date string for the job (YYYY-MM-DD)
        timestamp_str: Timestamp string for the job
        
    Returns:
        Dict containing load results
    """
    try:
        logger.info(f"Starting load job for {date_str} at {timestamp_str}")

        if not gcs_client or not firestore_db:
            logger.critical("GCS or Firestore client not initialized. Load service cannot proceed.")
            return {
                "status": "error",
                "message": "GCS or Firestore client not initialized",
                "job_date": date_str,
                "job_timestamp": timestamp_str,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

        # Ensure all required Firestore collections exist
        ensure_firestore_collections_exist()

        # Download all enriched files (NEW PIPELINE: enriched instead of transformed)
        articles = download_jsonl_file_from_gcs(
            bucket_name=GCS_BUCKET_NAME,
            gcs_prefix="enriched-news",  # Changed from transformed-news
            date_str=date_str,
            timestamp_str=timestamp_str,
            filename="enriched_articles.jsonl"  # Changed from transformed_articles.jsonl
        )

        # Download categories and topics (now generated by enrich service)
        categories = download_jsonl_file_from_gcs(
            bucket_name=GCS_BUCKET_NAME,
            gcs_prefix="enriched-news",  # Changed from transformed-news
            date_str=date_str,
            timestamp_str=timestamp_str,
            filename="categories.jsonl"  # Changed from transformed_categories.jsonl
        )

        topics = download_jsonl_file_from_gcs(
            bucket_name=GCS_BUCKET_NAME,
            gcs_prefix="enriched-news",  # Changed from transformed-news
            date_str=date_str,
            timestamp_str=timestamp_str,
            filename="topics.jsonl"  # Changed from transformed_topics.jsonl
        )

        # Download subcategories (NEW)
        subcategories = download_jsonl_file_from_gcs(
            bucket_name=GCS_BUCKET_NAME,
            gcs_prefix="enriched-news",
            date_str=date_str,
            timestamp_str=timestamp_str,
            filename="subcategories.jsonl"
        )

        # Download sources (now from enrich service)
        sources = download_jsonl_file_from_gcs(
            bucket_name=GCS_BUCKET_NAME,
            gcs_prefix="enriched-news",  # Changed from transform service to enrich service
            date_str=date_str,
            timestamp_str=timestamp_str,
            filename="transformed_sources.jsonl"
        )

        # Check if we have data
        if not articles and not categories and not topics and not sources:
            logger.error(f"No transformed data found from GCS path: {TRANSFORMED_DATA_GCS_PREFIX}/{date_str}/{timestamp_str}/")
            return {
                "status": "error",
                "message": "No transformed data found",
                "job_date": date_str,
                "job_timestamp": timestamp_str,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

        # Upload each dataset with specialized deduplication logic
        if articles:
            upload_articles_to_firestore(articles)
        
        # Upload sources before categories/topics to ensure they complete even if categories get stuck
        if sources:
            upload_sources_to_firestore(sources)
        
        # Use new subcollection structure
        if categories or topics:
            logger.info("Using new Firestore subcollection structure for categories and topics")
            upload_categories_and_topics_to_firestore(categories or [], topics or [])

        # Upload subcategories (NEW)
        if subcategories:
            logger.info(f"Uploading {len(subcategories)} subcategories to Firestore")
            upload_subcategories_to_firestore(subcategories)
        else:
            logger.warning("No subcategories found to upload")

        logger.info("Load Service finished successfully.")

        # Note: Embed service is triggered in parallel by enrich service (original architecture)
        # Extract → Transform → Enrich → [Load + Embed] → Recommendation

        # Calculate counts for response
        articles_count = len(articles) if articles else 0
        categories_count = len(categories) if categories else 0
        topics_count = len(topics) if topics else 0
        subcategories_count = len(subcategories) if subcategories else 0
        sources_count = len(sources) if sources else 0

        # Create message based on what was processed
        catalogue_msg = f"{categories_count} categories, {topics_count} topics, {subcategories_count} subcategories"

        return {
            "status": "success",
            "message": f"Load completed successfully. {articles_count} articles, {catalogue_msg}, {sources_count} sources processed",
            "job_date": date_str,
            "job_timestamp": timestamp_str,
            "articles_processed": articles_count,
            "categories_processed": categories_count,
            "topics_processed": topics_count,
            "subcategories_processed": subcategories_count,
            "sources_processed": sources_count,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
    except Exception as e:
        logger.error(f"Load job failed: {str(e)}", exc_info=True)
        return {
            "status": "error",
            "message": f"Load failed: {str(e)}",
            "job_date": date_str,
            "job_timestamp": timestamp_str,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

# === Cloud Function HTTP Handlers ===

@http
def load_service(request: flask.Request) -> flask.Response:
    """HTTP Cloud Function entry point for load service."""
    try:
        # Handle CORS
        if request.method == 'OPTIONS':
            headers = {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, POST',
                'Access-Control-Allow-Headers': 'Content-Type',
                'Access-Control-Max-Age': '3600'
            }
            return ('', 204, headers)

        # Health check
        if request.path == '/health':
            return flask.jsonify({
                'status': 'healthy',
                'service': 'load-service',
                'timestamp': datetime.now(timezone.utc).isoformat()
            })

        # Main load endpoint
        if request.method == 'POST':
            try:
                request_json = request.get_json(silent=True)
                if not request_json:
                    return flask.jsonify({'error': 'No JSON data provided'}), 400

                # Extract parameters - handle both old and new parameter names
                date_str = request_json.get('date_str') or request_json.get('job_date_str')
                timestamp_str = request_json.get('run_timestamp_str') or request_json.get('job_run_timestamp_str')
                
                if not date_str or not timestamp_str:
                    return flask.jsonify({
                        'error': 'Missing required parameters: date_str/job_date_str, run_timestamp_str/job_run_timestamp_str'
                    }), 400

                logger.info(f"Load service triggered for date: {date_str}, timestamp: {timestamp_str}")

                # Run load job
                result = run_load_job(date_str, timestamp_str)

                status_code = 200 if result.get('status') == 'success' else 500
                return flask.jsonify(result), status_code

            except Exception as e:
                logger.error(f"Error in load service: {e}", exc_info=True)
                return flask.jsonify({
                    'status': 'error',
                    'error': str(e),
                    'timestamp': datetime.now(timezone.utc).isoformat()
                }), 500

        return flask.jsonify({'error': 'Method not allowed'}), 405

    except Exception as e:
        logger.error(f"Unexpected error in load service: {e}", exc_info=True)
        return flask.jsonify({
            'status': 'error',
            'error': 'Internal server error',
            'timestamp': datetime.now(timezone.utc).isoformat()
        }), 500

# --- Main Execution for local testing ---
def main():
    parser = argparse.ArgumentParser(description="Load transformed news data into Firestore.")
    parser.add_argument("--date", required=True, help="Date in YYYY-MM-DD format for the data to process.")
    parser.add_argument("--timestamp", required=True, help="Timestamp in HH-MM-SS format for the specific run.")
    args = parser.parse_args()

    logger.info(f"Starting load service for date: {args.date}, timestamp: {args.timestamp}")

    result = run_load_job(args.date, args.timestamp)
    
    if result.get('status') == 'error':
        logger.error(f"Load job failed: {result.get('message')}")
        sys.exit(1)
    else:
        logger.info(f"Load job completed successfully: {result.get('message')}")

if __name__ == "__main__":
    main() 