#!/bin/bash

# Deploy script for Load Service (Cloud Function)
set -e

echo "🚀 Deploying Load Service to Google Cloud Functions..."

# Configuration
FUNCTION_NAME="load-service"
REGION="us-central1"
PROJECT_ID="news-reporter-13104"

# Deploy Cloud Function
gcloud functions deploy $FUNCTION_NAME \
  --gen2 \
  --runtime python311 \
  --region $REGION \
  --project $PROJECT_ID \
  --source . \
  --entry-point load_service \
  --trigger-http \
  --allow-unauthenticated \
  --memory 2Gi \
  --timeout 540s \
  --max-instances 10 \
  --set-env-vars "PROJECT_ID=$PROJECT_ID" \
  --set-env-vars "GCS_BUCKET_NAME=newsreporter" \
  --set-env-vars "TRANSFORMED_DATA_GCS_PREFIX=enriched-news" \
  --set-env-vars "FIRESTORE_ARTICLE_COLLECTION=articles" \
  --set-env-vars "FIRESTORE_SOURCES_COLLECTION=sources" \
  --set-env-vars "FIRESTORE_CATALOGUE_COLLECTION=categories" \
  --set-env-vars "FIRESTORE_SUBCATEGORIES_COLLECTION=subcategories" \
  --set-env-vars "TRANSFORM_SERVICE_URL=https://transform-service-b3jbgxlmra-uc.a.run.app"

echo "✅ Load Service deployed successfully!"
echo "🔗 Function URL: https://$REGION-$PROJECT_ID.cloudfunctions.net/$FUNCTION_NAME" 