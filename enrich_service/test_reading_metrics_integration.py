#!/usr/bin/env python3
"""
Test script to verify that reading metrics are properly integrated into the enrich service.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_reading_metrics_calculator():
    """Test that the reading metrics calculator works correctly"""
    print("Testing reading metrics calculator...")
    
    try:
        from reading_metrics import reading_metrics_calculator
        
        # Test article
        test_article = {
            'uri': 'test-article-123',
            'title': 'Breaking News: Major Development in Technology',
            'body': '''
            This is a test article with sufficient content to calculate meaningful reading metrics.
            The article contains multiple sentences and paragraphs to test the calculation algorithms.
            
            Reading time estimation depends on word count and average reading speeds.
            Different readers have different speeds, from slow to fast readers.
            
            The complexity analysis examines sentence structure and vocabulary difficulty.
            Technical terms and long sentences increase the complexity score.
            
            This article should provide enough content for accurate metric calculations.
            The reading metrics include time estimates, difficulty levels, and accessibility scores.
            ''',
            'summary': 'A test article for reading metrics validation.'
        }
        
        # Calculate metrics
        metrics = reading_metrics_calculator.calculate_all_metrics(test_article)
        
        print("✅ Reading metrics calculator works!")
        print(f"📊 Metrics calculated:")
        print(f"   - Word count: {metrics.get('word_metrics', {}).get('word_count', 'N/A')}")
        print(f"   - Reading time (avg): {metrics.get('reading_times', {}).get('average', {}).get('formatted', 'N/A')}")
        print(f"   - Difficulty: {metrics.get('difficulty_level', 'N/A')}")
        print(f"   - Grade level: {metrics.get('accessibility', {}).get('grade_level', 'N/A')}")
        
        return True, metrics
        
    except Exception as e:
        print(f"❌ Reading metrics calculator failed: {e}")
        return False, None

def test_reading_metrics_integration():
    """Test that reading metrics are properly integrated"""
    print("\nTesting reading metrics integration...")

    try:
        from reading_metrics import reading_metrics_calculator

        # Test article with more content
        test_article = {
            'uri': 'test-article-456',
            'title': 'Technology Innovation Drives Market Growth',
            'body': '''
            Technology companies are experiencing unprecedented growth as innovation drives market expansion.
            Artificial intelligence and machine learning technologies are transforming industries worldwide.

            Investment in research and development has increased significantly over the past year.
            Companies are focusing on sustainable solutions and digital transformation initiatives.

            Market analysts predict continued growth in the technology sector throughout the next decade.
            Consumer demand for innovative products and services remains strong across all demographics.

            The integration of artificial intelligence into business processes has revolutionized operational efficiency.
            Companies are leveraging machine learning algorithms to optimize their supply chain management systems.
            ''',
            'summary': 'Technology companies drive market growth through AI and innovation.'
        }

        # Calculate reading metrics directly
        metrics = reading_metrics_calculator.calculate_all_metrics(test_article)

        # Check if all expected metrics are present
        expected_sections = [
            'reading_times',
            'word_metrics',
            'complexity_metrics',
            'difficulty_level',
            'engagement_metrics',
            'structure_metrics',
            'attention_metrics',
            'accessibility'
        ]

        missing_sections = []
        present_sections = []

        for section in expected_sections:
            if section in metrics:
                present_sections.append(section)
            else:
                missing_sections.append(section)

        if missing_sections:
            print(f"❌ Missing metrics sections: {missing_sections}")
            print(f"✅ Present sections: {present_sections}")
            return False
        else:
            print("✅ All reading metrics sections present!")
            print(f"📊 Reading metrics summary:")
            print(f"   - Word count: {metrics['word_metrics']['word_count']}")
            print(f"   - Reading time (avg): {metrics['reading_times']['average']['formatted']}")
            print(f"   - Difficulty: {metrics['difficulty_level']}")
            print(f"   - Grade level: {metrics['accessibility']['grade_level']}")
            print(f"   - General audience: {metrics['accessibility']['suitable_for_general_audience']}")
            print(f"   - Attention level: {metrics['attention_metrics']['attention_level']}")
            return True

    except Exception as e:
        print(f"❌ Reading metrics integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all reading metrics integration tests"""
    print("Testing reading metrics integration in enrich service...\n")
    
    tests = [
        test_reading_metrics_calculator,
        test_reading_metrics_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n{'='*60}")
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All reading metrics integration tests passed!")
        print("\n📋 Summary:")
        print("• Reading metrics calculator is working correctly")
        print("• Enrich service properly integrates reading metrics")
        print("• All expected fields are present in enriched articles")
        print("• Reading time, difficulty, and accessibility data available")
        return True
    else:
        print("❌ Some reading metrics integration tests failed")
        print("\n🔍 Troubleshooting:")
        print("• Check if reading_metrics.py module is properly imported")
        print("• Verify that enrich_article function includes reading metrics calculation")
        print("• Ensure all dependencies are available")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
