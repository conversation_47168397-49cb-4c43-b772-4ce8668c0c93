#!/usr/bin/env python3
"""
Test script for source extraction functionality in enrich service.
Tests the new transformed_sources.jsonl generation feature.
"""

import sys
import os
import json
from datetime import datetime, timezone

# Add the enrich_service directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the source extraction functions
from http_server import extract_sources_from_articles, generate_source_image_url, extract_initials

def test_extract_initials():
    """Test the extract_initials function."""
    print("Testing extract_initials function...")
    
    test_cases = [
        ("BBC News", "BN"),  # BBC (3 chars) + News (4 chars) = BN
        ("The New York Times", "NYT"),  # New (3) + York (4) + Times (5) = NYT
        ("Reuters", "R"),  # Reuters (7 chars) = R (limited to first char when single word > 2)
        ("CNN", "C"),  # CNN (3 chars) = C (first letter of word > 2 chars)
        ("", "NS"),  # Empty string
        ("Analytics Insight", "AI")  # Analytics (9) + Insight (7) = AI
    ]
    
    for source_title, expected in test_cases:
        result = extract_initials(source_title)
        print(f"  {source_title} -> {result} (expected: {expected})")
        assert result == expected, f"Expected {expected}, got {result}"
    
    print("✅ extract_initials tests passed!")

def test_generate_source_image_url():
    """Test the generate_source_image_url function."""
    print("\nTesting generate_source_image_url function...")
    
    test_cases = [
        "analyticsinsight.net",
        "https://www.bbc.com",
        "https://cnn.com",
        "reuters.com"
    ]
    
    for source_uri in test_cases:
        result = generate_source_image_url(source_uri)
        print(f"  {source_uri} -> {result}")
        
        # Check that the URL contains the expected components
        assert "img.logo.dev" in result
        assert "token=pk_dhMaVdjySqiMtC3Loca6xQ" in result
        assert "size=200" in result
        assert "format=png" in result
    
    print("✅ generate_source_image_url tests passed!")

def test_extract_sources_from_articles():
    """Test the extract_sources_from_articles function."""
    print("\nTesting extract_sources_from_articles function...")
    
    # Sample enriched articles data
    sample_articles = [
        {
            "uri": "article1",
            "title": "Test Article 1",
            "source_name": "BBC News",
            "source_uri": "bbc.com",
            "url": "https://www.bbc.com/news/test-article-1",
            "source_location_place": "London",
            "source_location_country": "United Kingdom",
            "article_location": {
                "country": "UK",
                "coordinates": {"lat": 51.5074, "lon": -0.1278}
            }
        },
        {
            "uri": "article2", 
            "title": "Test Article 2",
            "source_name": "CNN",
            "source_uri": "cnn.com",
            "url": "https://www.cnn.com/news/test-article-2",
            "source_location_place": "Atlanta",
            "source_location_country": "United States"
        },
        {
            "uri": "article3",
            "title": "Test Article 3", 
            "source_name": "BBC News",  # Duplicate source - should be deduplicated
            "source_uri": "bbc.com",
            "url": "https://www.bbc.com/news/test-article-3"
        },
        {
            "uri": "article4",
            "title": "Test Article 4",
            "source_name": "Analytics Insight",
            "source_uri": "analyticsinsight.net",
            "url": "https://www.analyticsinsight.net/test-article-4"
        }
    ]
    
    # Extract sources
    sources = extract_sources_from_articles(sample_articles)
    
    print(f"  Extracted {len(sources)} sources from {len(sample_articles)} articles")
    
    # Should have 3 unique sources (BBC News appears twice)
    assert len(sources) == 3, f"Expected 3 sources, got {len(sources)}"
    
    # Check that all required Firestore fields are present
    required_fields = [
        'created_at', 'description', 'lastUpdated', 'last_article_date', 
        'last_updated', 'load_timestamp', 'source_image_url', 'source_name', 'source_url'
    ]
    
    for source in sources:
        print(f"\n  Source: {source['source_name']}")
        for field in required_fields:
            assert field in source, f"Missing required field: {field}"
            print(f"    {field}: {source[field]}")
        
        # Check location fields
        if source['source_name'] == 'BBC News':
            assert source['source_location_place'] == 'London'
            assert source['source_location_country'] == 'United Kingdom'
            assert 'location' in source
        
        # Check image URL format
        assert "img.logo.dev" in source['source_image_url']
        assert "token=pk_dhMaVdjySqiMtC3Loca6xQ" in source['source_image_url']
    
    print("✅ extract_sources_from_articles tests passed!")

def test_sources_jsonl_format():
    """Test that sources can be properly serialized to JSONL format."""
    print("\nTesting JSONL serialization...")
    
    sample_articles = [
        {
            "uri": "article1",
            "title": "Test Article",
            "source_name": "Test Source",
            "source_uri": "test.com",
            "url": "https://test.com/article"
        }
    ]
    
    sources = extract_sources_from_articles(sample_articles)
    
    # Test JSONL serialization
    jsonl_data = '\n'.join(json.dumps(source) for source in sources)
    print(f"  JSONL data length: {len(jsonl_data)} characters")
    
    # Test that it can be parsed back
    lines = jsonl_data.strip().split('\n')
    for line in lines:
        parsed = json.loads(line)
        assert 'source_name' in parsed
        assert 'source_image_url' in parsed
    
    print("✅ JSONL serialization tests passed!")

if __name__ == "__main__":
    print("🧪 Testing Source Extraction Functionality\n")
    
    try:
        test_extract_initials()
        test_generate_source_image_url()
        test_extract_sources_from_articles()
        test_sources_jsonl_format()
        
        print("\n🎉 All tests passed! Source extraction functionality is working correctly.")
        print("\n📋 Summary:")
        print("✅ Source extraction from articles")
        print("✅ Source deduplication")
        print("✅ Required Firestore fields generation")
        print("✅ Location information inclusion")
        print("✅ Logo.dev image URL generation with token")
        print("✅ JSONL serialization")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
