"""
Enhanced News Summarization Service - Production Entry Point

Fast, cost-effective news article processing using GPT-4.1-nano.
Provides summaries, haikus, and categorization for news articles.
"""

import asyncio
import logging
from typing import List, Optional
from summarizer import EnhancedSummarizationService
from models import OriginalArticleData, BatchProcessingResult
from config import LLM_CONFIG

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class EnhancedSummarizationApp:
    """Main application class for the enhanced summarization service."""
    
    def __init__(self):
        """Initialize the application."""
        self.summarizer = None
        self.sampler = None
        
    async def initialize(self, model: str = "gpt-4.1-nano"):
        """Initialize the service components."""
        try:
            # Initialize the summarization service
            self.summarizer = EnhancedSummarizationService(model=model)

            logger.info(f"Enhanced Summarization Service initialized with {model}")

        except Exception as e:
            logger.error(f"Failed to initialize service: {e}")
            raise
    
    async def process_articles_from_gcs(self,
                                      sample_size: int = 100,
                                      concurrent_requests: int = 5,
                                      max_files: int = 3) -> BatchProcessingResult:
        """Process articles sampled from GCS - DEPRECATED: Use HTTP server instead."""
        raise NotImplementedError("GCS sampling functionality moved to HTTP server. Use /process-batch endpoint instead.")
    
    async def process_specific_articles(self, 
                                      articles: List[OriginalArticleData],
                                      concurrent_requests: int = 5) -> BatchProcessingResult:
        """Process a specific list of articles."""
        if not self.summarizer:
            raise RuntimeError("Service not initialized. Call initialize() first.")
        
        logger.info(f"Processing {len(articles)} specific articles")
        
        result = await self.summarizer.process_articles_batch(
            articles=articles,
            concurrent_requests=concurrent_requests
        )
        
        return result
    
    def get_cost_analysis(self, article_count: int):
        """Get cost analysis for processing articles."""
        if not self.summarizer:
            raise RuntimeError("Service not initialized. Call initialize() first.")
        
        return self.summarizer.calculate_cost_analysis(article_count)

async def main():
    """Main entry point for the service."""
    try:
        # Initialize the application
        app = EnhancedSummarizationApp()
        await app.initialize(model="gpt-4.1-nano")

        logger.info("Enhanced Summarization Service initialized successfully")
        logger.info("Use HTTP server endpoints to process articles:")
        logger.info("  POST /process-batch - Process articles from GCS")
        logger.info("  POST /process-articles - Process specific articles")

        # Service is ready - HTTP server will handle requests
        return app
        
    except Exception as e:
        logger.error(f"Application error: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
