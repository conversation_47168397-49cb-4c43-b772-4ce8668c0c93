"""
Enhanced News Summarization Service

Production-ready LLM service for news article summarization using GPT-4.1-nano.
Provides fast, cost-effective article processing with summaries, haikus, and categorization.
"""

import os
import json
import time
import logging
import asyncio
from typing import List, Optional, Dict, Any
import openai
from datetime import datetime

# Import our models and config
from models import (
    OriginalArticleData, 
    EnhancedArticleResult, 
    EnhancedSummarizationResponse,
    BatchProcessingResult,
    CostAnalysis
)
from config import SUMMARIZATION_PROMPT, LLM_CONFIG
from gcp_secrets import get_openai_key_from_gcp

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnhancedSummarizationService:
    """Production LLM service for enhanced news summarization."""
    
    def __init__(self, api_key: Optional[str] = None, model: str = "gpt-4.1-nano"):
        """Initialize with OpenAI API key and model."""
        if not api_key:
            # Try to get from GCP Secrets Manager
            try:
                api_key = get_openai_key_from_gcp("OPENAI_API_KEY")
            except Exception as e:
                logger.error(f"Failed to get API key from secrets: {e}")
                raise ValueError("OpenAI API key is required")
        
        self.client = openai.OpenAI(api_key=api_key)
        self.model = model
        self.max_tokens = 300  # Optimized for speed
        self.temperature = 0.1
        self.prompt_template = self._load_prompt()
        
        logger.info(f"Initialized Enhanced Summarization Service with {model}")
    
    def _load_prompt(self) -> str:
        """Load the summarization prompt."""
        return SUMMARIZATION_PROMPT
    
    def _create_prompt_for_article(self, article: OriginalArticleData) -> str:
        """Create the full prompt for a specific article."""
        # Truncate article body to manage token usage and speed
        max_body_length = 2000  # Optimized for speed
        body = article.body[:max_body_length]
        if len(article.body) > max_body_length:
            body += "..."
        
        full_prompt = f"{self.prompt_template}\n\nTitle: {article.title}\n\nContent: {body}"
        return full_prompt
    
    async def _call_openai_api(self, prompt: str, retry_count: int = 0) -> EnhancedSummarizationResponse:
        """Make an API call to OpenAI with structured output using Pydantic models."""
        max_retries = 3  # Allow up to 3 retries for validation errors

        try:
            response = self.client.beta.chat.completions.parse(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                response_format=EnhancedSummarizationResponse,
                max_tokens=self.max_tokens,
                temperature=self.temperature,
                timeout=30  # 30 second timeout for faster failure
            )

            if retry_count > 0:
                logger.info(f"✅ Retry successful on attempt {retry_count + 1}")

            return response.choices[0].message.parsed

        except Exception as e:
            error_str = str(e)

            # Check if it's a validation error and we haven't exceeded retries
            if "validation error" in error_str and retry_count < max_retries:
                logger.warning(f"🔄 Validation error on attempt {retry_count + 1}/{max_retries + 1}, retrying...")
                logger.debug(f"Validation error details: {error_str}")

                # Create enhanced retry prompt with specific guidance
                retry_prompt = self._create_retry_prompt(prompt, error_str)
                return await self._call_openai_api(retry_prompt, retry_count + 1)

            # Log final failure after all retries exhausted
            if "validation error" in error_str:
                logger.error(f"❌ Validation failed after {max_retries + 1} attempts. Moving on to prevent infinite loop.")
                logger.error(f"Final validation error: {error_str}")
            else:
                logger.error(f"❌ OpenAI API error: {e}")

            raise

    def _create_retry_prompt(self, original_prompt: str, error_message: str) -> str:
        """Create an enhanced prompt for retry attempts with specific error guidance."""

        # Extract specific validation error details
        retry_guidance = ""
        if "not valid for category" in error_message:
            retry_guidance = """
IMPORTANT: The previous attempt had a validation error. Please pay special attention to:
• Ensure subcategories match their correct parent categories
• "social issues" belongs to "general", not "human interest"
• "infrastructure" belongs to "general", not "business"
• "community" belongs to "general", not "lifestyle"
• Double-check the category-subcategory mapping in the provided lists
"""
        elif "Summary too long" in error_message or "complete sentences" in error_message or "reference the article" in error_message:
            retry_guidance = """
IMPORTANT: The previous attempt had a summary validation error. Please ensure:
• Summary is exactly 100 words or fewer
• Summary ends with complete sentences (., !, or ?)
• Do not cut off mid-sentence
• Do NOT use phrases like "The article discusses" or "This piece talks about"
• Present information as factual statements of events and developments
• Write concise but complete thoughts
"""

        # Add retry guidance to the original prompt
        enhanced_prompt = original_prompt.replace(
            "**Important Guidelines:**",
            f"{retry_guidance}\n**Important Guidelines:**"
        )

        return enhanced_prompt

    def _parse_llm_response(self, response: str) -> EnhancedSummarizationResponse:
        """Parse and validate LLM response using Pydantic."""
        try:
            # Clean the response
            response = response.strip()
            if response.startswith('```json'):
                response = response[7:]
            if response.endswith('```'):
                response = response[:-3]
            response = response.strip()
            
            # Parse JSON
            response_data = json.loads(response)
            
            # Handle category mapping for common LLM mistakes
            if 'category' in response_data:
                category = response_data['category'].lower()
                # Map common categories that aren't in our enum to 'general'
                if category in ['crime', 'law', 'legal', 'law enforcement', 'education']:
                    response_data['category'] = 'general'
            
            # Validate using Pydantic model
            return EnhancedSummarizationResponse(**response_data)
            
        except Exception as e:
            logger.error(f"Failed to parse LLM response: {e}")
            logger.error(f"Response was: {response}")
            raise
    
    async def process_single_article(self, article: OriginalArticleData) -> EnhancedArticleResult:
        """Process a single article through the enhanced summarization."""
        start_time = time.time()
        
        try:
            # Get original category
            original_category = article.original_categories[0] if article.original_categories else None
            
            # Create prompt
            prompt = self._create_prompt_for_article(article)
            
            # Call LLM with structured output
            parsed_response = await self._call_openai_api(prompt)
            
            # Calculate processing time
            processing_time = time.time() - start_time
            
            # Create result
            result = EnhancedArticleResult(
                uri=article.uri,
                title=article.title,
                original_category=original_category,
                original_concept_labels=article.concept_labels,
                source_title=article.source_title,
                summary=parsed_response.summary,
                haiku=parsed_response.haiku,
                key_takeaways=parsed_response.key_takeaways,
                llm_category=parsed_response.category.value,
                llm_subcategory=parsed_response.subcategory.value,
                processing_time=processing_time
            )
            
            logger.info(f"Successfully processed article {article.uri}")
            return result
            
        except Exception as e:
            # Create error result
            processing_time = time.time() - start_time
            result = EnhancedArticleResult(
                uri=article.uri,
                title=article.title,
                original_category=original_category,
                original_concept_labels=article.concept_labels,
                source_title=article.source_title,
                summary="",
                haiku="",
                key_takeaways=[],
                llm_category="",
                llm_subcategory="",
                processing_time=processing_time,
                error=str(e)
            )
            
            logger.error(f"Failed to process article {article.uri}: {e}")
            return result
    
    async def process_articles_batch(self, 
                                   articles: List[OriginalArticleData],
                                   concurrent_requests: int = 5) -> BatchProcessingResult:
        """Process multiple articles with concurrency control."""
        logger.info(f"Processing {len(articles)} articles with {concurrent_requests} concurrent requests")
        
        # Create semaphore to limit concurrent requests
        semaphore = asyncio.Semaphore(concurrent_requests)
        
        # Progress tracking
        completed_count = 0
        total_count = len(articles)
        
        async def process_with_semaphore(article):
            nonlocal completed_count
            async with semaphore:
                result = await self.process_single_article(article)
                completed_count += 1
                progress_pct = (completed_count / total_count) * 100
                logger.info(f"Progress: {completed_count}/{total_count} ({progress_pct:.1f}%) - Latest: {article.uri}")
                return result
        
        # Process all articles concurrently
        start_time = time.time()
        tasks = [process_with_semaphore(article) for article in articles]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        total_time = time.time() - start_time
        
        # Process results and handle exceptions
        processed_results = []
        errors = []
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                # Handle exceptions from gather
                article = articles[i]
                error_result = EnhancedArticleResult(
                    uri=article.uri,
                    title=article.title,
                    original_category=article.original_categories[0] if article.original_categories else None,
                    original_concept_labels=article.concept_labels,
                    source_title=article.source_title,
                    summary="",
                    haiku="",
                    llm_category="",
                    llm_subcategory="",
                    error=str(result)
                )
                processed_results.append(error_result)
                errors.append(str(result))
            else:
                processed_results.append(result)
        
        # Calculate metrics
        successful = sum(1 for r in processed_results if not r.error)
        failed = len(processed_results) - successful
        success_rate = (successful / len(processed_results)) * 100 if processed_results else 0
        
        # Calculate average processing time for successful articles
        successful_times = [r.processing_time for r in processed_results if r.processing_time and not r.error]
        avg_time = sum(successful_times) / len(successful_times) if successful_times else 0
        
        # Create batch result
        batch_result = BatchProcessingResult(
            total_articles=len(articles),
            successful_articles=successful,
            failed_articles=failed,
            success_rate=success_rate,
            average_processing_time=avg_time,
            total_processing_time=total_time,
            results=processed_results,
            errors=errors
        )
        
        logger.info(f"Batch processing completed: {successful}/{len(articles)} successful ({success_rate:.1f}%)")
        return batch_result
    
    def calculate_cost_analysis(self, article_count: int) -> CostAnalysis:
        """Calculate cost analysis for processing articles."""
        return CostAnalysis.calculate_cost(
            articles_count=article_count,
            model=self.model
        )
