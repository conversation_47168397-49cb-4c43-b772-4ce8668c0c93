#!/usr/bin/env python3
"""
Test script to verify that the 100-word summary limit is working correctly.
"""

from models import EnhancedSummarizationResponse, NewsCategory, GeneralSubcategory, PoliticsSubcategory

def test_100_word_limit():
    """Test that summaries with 100 words or fewer pass validation"""
    
    # Test with exactly 100 words
    summary_100_words = " ".join([f"word{i}" for i in range(1, 101)]) + "."
    
    try:
        response = EnhancedSummarizationResponse(
            summary=summary_100_words,
            haiku="Breaking news today\nImportant events unfold\nStory continues",
            category=NewsCategory.GENERAL,
            subcategory=GeneralSubcategory.SOCIAL_ISSUES
        )
        print(f"✅ 100-word summary PASSED validation")
        print(f"   Word count: {len(summary_100_words.split())}")
    except Exception as e:
        print(f"❌ 100-word summary FAILED validation: {e}")
        return False
    
    # Test with 101 words (should fail)
    summary_101_words = " ".join([f"word{i}" for i in range(1, 102)]) + "."
    
    try:
        response = EnhancedSummarizationResponse(
            summary=summary_101_words,
            haiku="Breaking news today\nImportant events unfold\nStory continues",
            category=NewsCategory.GENERAL,
            subcategory=GeneralSubcategory.SOCIAL_ISSUES
        )
        print(f"❌ 101-word summary should have FAILED but PASSED validation")
        return False
    except Exception as e:
        print(f"✅ 101-word summary correctly FAILED validation: {e}")
        print(f"   Word count: {len(summary_101_words.split())}")
    
    # Test with 80 words (should still pass)
    summary_80_words = " ".join([f"word{i}" for i in range(1, 81)]) + "."
    
    try:
        response = EnhancedSummarizationResponse(
            summary=summary_80_words,
            haiku="Breaking news today\nImportant events unfold\nStory continues",
            category=NewsCategory.GENERAL,
            subcategory=GeneralSubcategory.SOCIAL_ISSUES
        )
        print(f"✅ 80-word summary PASSED validation")
        print(f"   Word count: {len(summary_80_words.split())}")
    except Exception as e:
        print(f"❌ 80-word summary FAILED validation: {e}")
        return False
    
    # Test with realistic summary (around 90 words)
    realistic_summary = """
    President Biden announced new climate initiatives during a White House press conference yesterday. 
    The comprehensive plan includes $50 billion in federal funding for renewable energy projects across 
    twelve states. Environmental groups praised the announcement while Republican lawmakers criticized 
    the spending as excessive. The initiative aims to reduce carbon emissions by 30% within five years. 
    Implementation begins next quarter with solar panel installations in California, Texas, and Florida. 
    Energy Secretary Jennifer Granholm will oversee the program's rollout and coordinate with state officials.
    """.strip()
    
    try:
        response = EnhancedSummarizationResponse(
            summary=realistic_summary,
            haiku="Climate action\nFifty billion for clean power\nFuture starts today",
            category=NewsCategory.POLITICS,
            subcategory=PoliticsSubcategory.GOVERNMENT_AGENCIES
        )
        word_count = len(realistic_summary.split())
        print(f"✅ Realistic summary PASSED validation")
        print(f"   Word count: {word_count}")
        print(f"   Summary: {realistic_summary[:100]}...")
    except Exception as e:
        print(f"❌ Realistic summary FAILED validation: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("Testing 100-word summary limit validation...\n")
    
    success = test_100_word_limit()
    
    print(f"\n=== FINAL RESULT ===")
    if success:
        print("🎉 ALL TESTS PASSED! The 100-word limit is working correctly.")
    else:
        print("⚠️  Some tests failed. Please review the validation logic.")
