"""
Dynamic Source Configuration Management
Allows runtime updates to news sources, fetch settings, and source priorities
"""

import json
import os
from typing import Dict, List, Any, Optional
# Using REST client instead of grpcio-based firestore
import sys
sys.path.append('../shared_utils')
try:
    from gcp_rest_client import FirestoreRestClient
except ImportError:
    FirestoreRestClient = None
from datetime import datetime, timezone
import logging

logger = logging.getLogger(__name__)

class SourceConfigManager:
    """Manages dynamic source configuration from Firestore"""
    
    def __init__(self):
        if FirestoreRestClient:
            self.firestore_db = FirestoreRestClient()
        else:
            self.firestore_db = None
            logger.warning("Firestore REST client not available, using fallback configuration")
        self.config_collection = "source_config"
        self._cache = {}
        self._cache_timestamp = None
        
    def get_source_config(self) -> Dict[str, Any]:
        """Get current source configuration with caching"""
        try:
            # Check cache (5 minute TTL)
            import time
            current_time = time.time()
            if (self._cache_timestamp and 
                current_time - self._cache_timestamp < 300 and 
                self._cache):
                return self._cache
            
            # Fetch from Firestore using REST client
            config_data = self.firestore_db.get_document(self.config_collection, "main")

            if config_data:
                self._cache = config_data
                self._cache_timestamp = current_time
                logger.info("Loaded source configuration from Firestore")
                return config_data
            else:
                # Create default configuration
                default_config = self._get_default_config()
                self._save_config(default_config)
                self._cache = default_config
                self._cache_timestamp = current_time
                logger.info("Created default source configuration")
                return default_config
                
        except Exception as e:
            logger.error(f"Failed to load source configuration: {e}")
            # Fallback to default
            return self._get_default_config()
    
    def update_source_config(self, config: Dict[str, Any]) -> bool:
        """Update source configuration in Firestore"""
        try:
            config['last_updated'] = datetime.now(timezone.utc).isoformat()
            success = self.firestore_db.set_document(self.config_collection, "main", config)
            if success:
                # Clear cache
                self._cache = {}
                self._cache_timestamp = None
                logger.info("Updated source configuration in Firestore")
                return True
            else:
                logger.error("Failed to update source configuration in Firestore")
                return False
        except Exception as e:
            logger.error(f"Failed to update source configuration: {e}")
            return False
    
    def get_sources_by_category(self, category: str) -> Dict[str, Any]:
        """Get sources for a specific category"""
        config = self.get_source_config()
        return config.get('sources', {}).get(category, {})
    
    def get_fetch_settings_by_category(self, category: str) -> Dict[str, Any]:
        """Get fetch settings for a specific category"""
        config = self.get_source_config()
        return config.get('fetch_settings', {}).get(category, {
            "total_pages_to_fetch": 1,
            "pagination_delay_seconds": 2,
            "time_window_hours": 12
        })
    
    def add_source(self, category: str, source_name: str, source_url: str, 
                   tier: str = "tier_3", weight: float = 0.6) -> bool:
        """Add a new source to a category"""
        config = self.get_source_config()
        
        if 'sources' not in config:
            config['sources'] = {}
        if category not in config['sources']:
            config['sources'][category] = {}
        
        config['sources'][category][source_name] = {
            'url': source_url,
            'tier': tier,
            'weight': weight,
            'enabled': True,
            'added_at': datetime.now(timezone.utc).isoformat()
        }
        
        return self.update_source_config(config)
    
    def remove_source(self, category: str, source_name: str) -> bool:
        """Remove a source from a category"""
        config = self.get_source_config()
        
        if (category in config.get('sources', {}) and 
            source_name in config['sources'][category]):
            del config['sources'][category][source_name]
            return self.update_source_config(config)
        
        return False
    
    def update_source(self, category: str, source_name: str, updates: Dict[str, Any]) -> bool:
        """Update a specific source's properties"""
        config = self.get_source_config()
        
        if (category in config.get('sources', {}) and 
            source_name in config['sources'][category]):
            config['sources'][category][source_name].update(updates)
            config['sources'][category][source_name]['updated_at'] = datetime.now(timezone.utc).isoformat()
            return self.update_source_config(config)
        
        return False
    
    def update_fetch_settings(self, category: str, settings: Dict[str, Any]) -> bool:
        """Update fetch settings for a category"""
        config = self.get_source_config()
        
        if 'fetch_settings' not in config:
            config['fetch_settings'] = {}
        
        config['fetch_settings'][category] = settings
        return self.update_source_config(config)
    
    def get_source_authority_map(self) -> Dict[str, Dict[str, Any]]:
        """Get source authority mapping for breaking news"""
        config = self.get_source_config()
        authority_map = {}
        
        for category, sources in config.get('sources', {}).items():
            for source_name, source_data in sources.items():
                if source_data.get('enabled', True):
                    # Extract domain from URL
                    url = source_data.get('url', '')
                    domain = self._extract_domain(url)
                    
                    if domain:
                        authority_map[domain] = {
                            'tier': source_data.get('tier', 'tier_3'),
                            'weight': source_data.get('weight', 0.6),
                            'category': category,
                            'source_name': source_name
                        }
        
        return authority_map
    
    def get_enabled_sources(self) -> Dict[str, Dict[str, Any]]:
        """Get all enabled sources organized by category"""
        config = self.get_source_config()
        enabled_sources = {}
        
        for category, sources in config.get('sources', {}).items():
            enabled_sources[category] = {}
            for source_name, source_data in sources.items():
                if source_data.get('enabled', True):
                    enabled_sources[category][source_name] = source_data
        
        return enabled_sources
    
    def toggle_source(self, category: str, source_name: str, enabled: bool) -> bool:
        """Enable/disable a specific source"""
        return self.update_source(category, source_name, {'enabled': enabled})
    
    def _extract_domain(self, url: str) -> str:
        """Extract domain from URL"""
        if not url:
            return ""
        
        domain = url.lower()
        if "://" in domain:
            domain = domain.split("://")[1]
        if "/" in domain:
            domain = domain.split("/")[0]
        if domain.startswith("www."):
            domain = domain[4:]
        
        return domain
    
    def _save_config(self, config: Dict[str, Any]):
        """Save configuration to Firestore"""
        try:
            self.firestore_db.set_document(self.config_collection, "main", config)
        except Exception as e:
            logger.error(f"Failed to save source configuration: {e}")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default source configuration"""
        return {
            "sources": {
                "politics": {
                    "BBC News": {"url": "https://www.bbc.com/", "tier": "tier_1", "weight": 1.0, "enabled": True},
                    "Reuters": {"url": "https://www.reuters.com/", "tier": "tier_1", "weight": 1.0, "enabled": True},
                    "Associated Press": {"url": "https://apnews.com/politics", "tier": "tier_1", "weight": 1.0, "enabled": True},
                    "CNN": {"url": "https://www.cnn.com/", "tier": "tier_1", "weight": 1.0, "enabled": True},
                    "The New York Times": {"url": "https://www.nytimes.com/", "tier": "tier_1", "weight": 1.0, "enabled": True},
                    "The Washington Post": {"url": "https://www.washingtonpost.com/", "tier": "tier_1", "weight": 1.0, "enabled": True},
                    "POLITICO": {"url": "https://www.politico.com/", "tier": "tier_2", "weight": 0.8, "enabled": True},
                    "The Hill": {"url": "https://thehill.com/", "tier": "tier_2", "weight": 0.8, "enabled": True}
                },
                "business": {
                    "Bloomberg": {"url": "https://www.bloomberg.com/", "tier": "tier_1", "weight": 1.0, "enabled": True},
                    "The Wall Street Journal": {"url": "https://www.wsj.com/", "tier": "tier_1", "weight": 1.0, "enabled": True},
                    "Financial Times": {"url": "https://www.ft.com/", "tier": "tier_1", "weight": 1.0, "enabled": True},
                    "CNBC": {"url": "https://www.cnbc.com/", "tier": "tier_2", "weight": 0.8, "enabled": True},
                    "Forbes": {"url": "https://www.forbes.com/", "tier": "tier_2", "weight": 0.8, "enabled": True}
                },
                "technology": {
                    "TechCrunch": {"url": "https://techcrunch.com/", "tier": "tier_2", "weight": 0.8, "enabled": True},
                    "The Verge": {"url": "https://www.theverge.com/", "tier": "tier_2", "weight": 0.8, "enabled": True},
                    "Wired": {"url": "https://www.wired.com/", "tier": "tier_2", "weight": 0.8, "enabled": True},
                    "Ars Technica": {"url": "https://arstechnica.com/", "tier": "tier_2", "weight": 0.8, "enabled": True}
                },
                "science": {
                    "Nature": {"url": "https://www.nature.com/", "tier": "tier_1", "weight": 1.0, "enabled": True},
                    "Science Magazine": {"url": "https://www.science.org/", "tier": "tier_1", "weight": 1.0, "enabled": True},
                    "Scientific American": {"url": "https://www.scientificamerican.com/", "tier": "tier_2", "weight": 0.8, "enabled": True},
                    "New Scientist": {"url": "https://www.newscientist.com/", "tier": "tier_2", "weight": 0.8, "enabled": True}
                },
                "health": {
                    "STAT News": {"url": "https://www.statnews.com/", "tier": "tier_1", "weight": 1.0, "enabled": True},
                    "Medical News Today": {"url": "https://www.medicalnewstoday.com/", "tier": "tier_2", "weight": 0.8, "enabled": True},
                    "Healthline": {"url": "https://www.healthline.com/", "tier": "tier_2", "weight": 0.8, "enabled": True}
                },
                "sports": {
                    "ESPN": {"url": "https://www.espn.com/", "tier": "tier_1", "weight": 1.0, "enabled": True},
                    "The Athletic": {"url": "https://theathletic.com/", "tier": "tier_1", "weight": 1.0, "enabled": True},
                    "Sports Illustrated": {"url": "https://www.si.com/", "tier": "tier_2", "weight": 0.8, "enabled": True}
                },
                "entertainment": {
                    "Variety": {"url": "https://variety.com/", "tier": "tier_1", "weight": 1.0, "enabled": True},
                    "The Hollywood Reporter": {"url": "https://www.hollywoodreporter.com/", "tier": "tier_1", "weight": 1.0, "enabled": True},
                    "Entertainment Weekly": {"url": "https://ew.com/", "tier": "tier_2", "weight": 0.8, "enabled": True}
                }
            },
            "fetch_settings": {
                "politics": {"total_pages_to_fetch": 2, "pagination_delay_seconds": 2, "time_window_hours": 12},
                "business": {"total_pages_to_fetch": 2, "pagination_delay_seconds": 2, "time_window_hours": 12},
                "technology": {"total_pages_to_fetch": 3, "pagination_delay_seconds": 2, "time_window_hours": 12},
                "science": {"total_pages_to_fetch": 1, "pagination_delay_seconds": 2, "time_window_hours": 12},
                "health": {"total_pages_to_fetch": 1, "pagination_delay_seconds": 2, "time_window_hours": 12},
                "sports": {"total_pages_to_fetch": 1, "pagination_delay_seconds": 2, "time_window_hours": 12},
                "entertainment": {"total_pages_to_fetch": 1, "pagination_delay_seconds": 2, "time_window_hours": 12},
                "trending": {"total_pages_to_fetch": 1, "pagination_delay_seconds": 1, "time_window_hours": 3, "sortBy": "socialScore", "articlesSortByAsc": False}
            },
            "global_settings": {
                "max_articles_per_category": 100,
                "default_pagination_delay": 2,
                "default_time_window_hours": 12,
                "enable_source_rotation": True,
                "source_timeout_seconds": 30
            },
            "last_updated": datetime.now(timezone.utc).isoformat(),
            "version": "1.0"
        }

# Global instance
source_config_manager = SourceConfigManager()
