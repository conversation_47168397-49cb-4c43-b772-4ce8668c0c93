"""
Reading Metrics Calculator
Calculates reading time, complexity, and engagement metrics for news articles
"""

import re
import math
from typing import Dict, Any, Optional
from datetime import datetime, timezone
import logging

logger = logging.getLogger(__name__)

class ReadingMetricsCalculator:
    """Calculates comprehensive reading metrics for articles"""
    
    def __init__(self):
        # Reading speed constants (words per minute)
        self.reading_speeds = {
            'slow': 150,      # Slow readers
            'average': 200,   # Average adult reading speed
            'fast': 250,      # Fast readers
            'speed': 300      # Speed readers
        }
        
        # Complexity factors
        self.complexity_weights = {
            'sentence_length': 0.3,
            'syllable_density': 0.25,
            'technical_terms': 0.2,
            'readability_score': 0.25
        }
    
    def calculate_all_metrics(self, article: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate comprehensive reading metrics for an article"""
        try:
            # Extract text content
            title = article.get('title', '')
            body = article.get('body', '')
            summary = article.get('summary', '')
            
            # Calculate basic metrics
            word_count = self._count_words(body)
            sentence_count = self._count_sentences(body)
            paragraph_count = self._count_paragraphs(body)
            
            # Calculate reading times
            reading_times = self._calculate_reading_times(word_count)
            
            # Calculate complexity metrics
            complexity_metrics = self._calculate_complexity(body)
            
            # Calculate engagement metrics
            engagement_metrics = self._calculate_engagement_metrics(article)
            
            # Calculate content structure metrics
            structure_metrics = self._calculate_structure_metrics(title, body, summary)
            
            # Determine reading difficulty
            difficulty_level = self._determine_difficulty_level(complexity_metrics)
            
            # Calculate estimated attention span required
            attention_metrics = self._calculate_attention_metrics(
                reading_times['average']['minutes'], complexity_metrics, structure_metrics
            )
            
            return {
                'reading_times': reading_times,
                'word_metrics': {
                    'word_count': word_count,
                    'sentence_count': sentence_count,
                    'paragraph_count': paragraph_count,
                    'avg_words_per_sentence': round(word_count / max(sentence_count, 1), 1),
                    'avg_sentences_per_paragraph': round(sentence_count / max(paragraph_count, 1), 1)
                },
                'complexity_metrics': complexity_metrics,
                'difficulty_level': difficulty_level,
                'engagement_metrics': engagement_metrics,
                'structure_metrics': structure_metrics,
                'attention_metrics': attention_metrics,
                'accessibility': self._calculate_accessibility_score(complexity_metrics),
                'calculated_at': datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error calculating reading metrics: {e}")
            return self._get_default_metrics()
    
    def _count_words(self, text: str) -> int:
        """Count words in text"""
        if not text:
            return 0
        # Remove extra whitespace and split
        words = re.findall(r'\b\w+\b', text.lower())
        return len(words)
    
    def _count_sentences(self, text: str) -> int:
        """Count sentences in text"""
        if not text:
            return 0
        # Split on sentence endings
        sentences = re.split(r'[.!?]+', text)
        # Filter out empty strings
        sentences = [s.strip() for s in sentences if s.strip()]
        return len(sentences)
    
    def _count_paragraphs(self, text: str) -> int:
        """Count paragraphs in text"""
        if not text:
            return 0
        # Split on double newlines or paragraph breaks
        paragraphs = re.split(r'\n\s*\n', text.strip())
        paragraphs = [p.strip() for p in paragraphs if p.strip()]
        return max(len(paragraphs), 1)
    
    def _calculate_reading_times(self, word_count: int) -> Dict[str, float]:
        """Calculate reading times for different reading speeds"""
        reading_times = {}
        
        for speed_type, wpm in self.reading_speeds.items():
            minutes = word_count / wpm
            reading_times[speed_type] = {
                'minutes': round(minutes, 1),
                'seconds': round(minutes * 60),
                'formatted': self._format_reading_time(minutes)
            }
        
        return reading_times
    
    def _format_reading_time(self, minutes: float) -> str:
        """Format reading time as human-readable string"""
        if minutes < 1:
            seconds = int(minutes * 60)
            return f"{seconds} sec"
        elif minutes < 60:
            return f"{int(minutes)} min"
        else:
            hours = int(minutes // 60)
            mins = int(minutes % 60)
            return f"{hours}h {mins}m"
    
    def _calculate_complexity(self, text: str) -> Dict[str, Any]:
        """Calculate text complexity metrics"""
        if not text:
            return {'flesch_score': 100, 'complexity_level': 'very_easy'}
        
        word_count = self._count_words(text)
        sentence_count = self._count_sentences(text)
        
        # Calculate average sentence length
        avg_sentence_length = word_count / max(sentence_count, 1)
        
        # Estimate syllable count (simplified)
        syllable_count = self._estimate_syllables(text)
        avg_syllables_per_word = syllable_count / max(word_count, 1)
        
        # Calculate Flesch Reading Ease Score
        flesch_score = self._calculate_flesch_score(
            avg_sentence_length, avg_syllables_per_word
        )
        
        # Count technical/complex terms
        technical_terms = self._count_technical_terms(text)
        
        return {
            'flesch_score': round(flesch_score, 1),
            'avg_sentence_length': round(avg_sentence_length, 1),
            'avg_syllables_per_word': round(avg_syllables_per_word, 2),
            'technical_terms_count': technical_terms,
            'technical_terms_ratio': round(technical_terms / max(word_count, 1) * 100, 1),
            'complexity_level': self._flesch_to_level(flesch_score)
        }
    
    def _estimate_syllables(self, text: str) -> int:
        """Estimate syllable count using simple heuristics"""
        words = re.findall(r'\b\w+\b', text.lower())
        total_syllables = 0
        
        for word in words:
            # Count vowel groups
            vowel_groups = len(re.findall(r'[aeiouy]+', word))
            # Adjust for silent e
            if word.endswith('e') and vowel_groups > 1:
                vowel_groups -= 1
            # Minimum of 1 syllable per word
            total_syllables += max(vowel_groups, 1)
        
        return total_syllables
    
    def _calculate_flesch_score(self, avg_sentence_length: float, avg_syllables_per_word: float) -> float:
        """Calculate Flesch Reading Ease Score"""
        return 206.835 - (1.015 * avg_sentence_length) - (84.6 * avg_syllables_per_word)
    
    def _flesch_to_level(self, score: float) -> str:
        """Convert Flesch score to difficulty level"""
        if score >= 90:
            return 'very_easy'
        elif score >= 80:
            return 'easy'
        elif score >= 70:
            return 'fairly_easy'
        elif score >= 60:
            return 'standard'
        elif score >= 50:
            return 'fairly_difficult'
        elif score >= 30:
            return 'difficult'
        else:
            return 'very_difficult'
    
    def _count_technical_terms(self, text: str) -> int:
        """Count technical/complex terms in text"""
        # Simple heuristic: words longer than 12 characters or containing specific patterns
        words = re.findall(r'\b\w+\b', text.lower())
        technical_count = 0
        
        technical_patterns = [
            r'\w{12,}',  # Very long words
            r'\w*tion\b',  # Words ending in -tion
            r'\w*ment\b',  # Words ending in -ment
            r'\w*ology\b',  # Words ending in -ology
            r'\w*ical\b',  # Words ending in -ical
        ]
        
        for word in words:
            for pattern in technical_patterns:
                if re.match(pattern, word):
                    technical_count += 1
                    break
        
        return technical_count
    
    def _calculate_engagement_metrics(self, article: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate engagement-related metrics"""
        title = article.get('title', '')
        body = article.get('body', '')
        
        # Count engaging elements
        question_count = len(re.findall(r'\?', body))
        exclamation_count = len(re.findall(r'!', body))
        quote_count = len(re.findall(r'["""\'\'"]', body))
        
        # Calculate title engagement
        title_word_count = self._count_words(title)
        title_engagement = self._calculate_title_engagement(title)
        
        return {
            'question_count': question_count,
            'exclamation_count': exclamation_count,
            'quote_count': quote_count,
            'title_word_count': title_word_count,
            'title_engagement_score': title_engagement,
            'interactive_elements': question_count + exclamation_count,
            'narrative_elements': quote_count
        }
    
    def _calculate_title_engagement(self, title: str) -> float:
        """Calculate title engagement score"""
        if not title:
            return 0.0
        
        score = 0.0
        title_lower = title.lower()
        
        # Engaging words
        engaging_words = ['breaking', 'exclusive', 'revealed', 'shocking', 'amazing', 'incredible']
        for word in engaging_words:
            if word in title_lower:
                score += 0.2
        
        # Questions in title
        if '?' in title:
            score += 0.3
        
        # Numbers in title
        if re.search(r'\d+', title):
            score += 0.1
        
        # Optimal length (6-12 words)
        word_count = self._count_words(title)
        if 6 <= word_count <= 12:
            score += 0.2
        
        return min(score, 1.0)
    
    def _calculate_structure_metrics(self, title: str, body: str, summary: str) -> Dict[str, Any]:
        """Calculate content structure metrics"""
        return {
            'has_summary': bool(summary),
            'summary_ratio': len(summary) / max(len(body), 1) if summary else 0,
            'title_body_ratio': len(title) / max(len(body), 1),
            'content_density': self._calculate_content_density(body),
            'structure_score': self._calculate_structure_score(title, body, summary)
        }
    
    def _calculate_content_density(self, text: str) -> float:
        """Calculate content density (information per character)"""
        if not text:
            return 0.0
        
        # Simple heuristic: ratio of meaningful characters to total characters
        meaningful_chars = len(re.findall(r'[a-zA-Z0-9]', text))
        total_chars = len(text)
        
        return meaningful_chars / max(total_chars, 1)
    
    def _calculate_structure_score(self, title: str, body: str, summary: str) -> float:
        """Calculate overall structure quality score"""
        score = 0.0
        
        # Has title
        if title:
            score += 0.2
        
        # Has body
        if body:
            score += 0.4
        
        # Has summary
        if summary:
            score += 0.2
        
        # Good length ratios
        if summary and body:
            summary_ratio = len(summary) / len(body)
            if 0.1 <= summary_ratio <= 0.3:  # Good summary length
                score += 0.2
        
        return score
    
    def _determine_difficulty_level(self, complexity_metrics: Dict[str, Any]) -> str:
        """Determine overall difficulty level"""
        return complexity_metrics.get('complexity_level', 'standard')
    
    def _calculate_attention_metrics(self, reading_time_minutes: float, 
                                   complexity_metrics: Dict[str, Any],
                                   structure_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate attention span and focus requirements"""
        base_attention = reading_time_minutes
        
        # Adjust for complexity
        complexity_level = complexity_metrics.get('complexity_level', 'standard')
        complexity_multipliers = {
            'very_easy': 0.8,
            'easy': 0.9,
            'fairly_easy': 0.95,
            'standard': 1.0,
            'fairly_difficult': 1.2,
            'difficult': 1.4,
            'very_difficult': 1.6
        }
        
        attention_required = base_attention * complexity_multipliers.get(complexity_level, 1.0)
        
        # Determine attention level
        if attention_required <= 2:
            attention_level = 'low'
        elif attention_required <= 5:
            attention_level = 'medium'
        elif attention_required <= 10:
            attention_level = 'high'
        else:
            attention_level = 'very_high'
        
        return {
            'attention_required_minutes': round(attention_required, 1),
            'attention_level': attention_level,
            'focus_breaks_recommended': math.ceil(attention_required / 5),
            'optimal_reading_session': min(attention_required, 15)  # Max 15 min sessions
        }
    
    def _calculate_accessibility_score(self, complexity_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate accessibility score for different reading levels"""
        flesch_score = complexity_metrics.get('flesch_score', 50)
        
        # Map to grade levels
        if flesch_score >= 90:
            grade_level = '5th grade'
            accessibility = 'very_high'
        elif flesch_score >= 80:
            grade_level = '6th grade'
            accessibility = 'high'
        elif flesch_score >= 70:
            grade_level = '7th grade'
            accessibility = 'good'
        elif flesch_score >= 60:
            grade_level = '8th-9th grade'
            accessibility = 'moderate'
        elif flesch_score >= 50:
            grade_level = '10th-12th grade'
            accessibility = 'low'
        elif flesch_score >= 30:
            grade_level = 'college level'
            accessibility = 'very_low'
        else:
            grade_level = 'graduate level'
            accessibility = 'expert_only'
        
        return {
            'grade_level': grade_level,
            'accessibility_level': accessibility,
            'flesch_score': flesch_score,
            'suitable_for_general_audience': flesch_score >= 60
        }
    
    def _get_default_metrics(self) -> Dict[str, Any]:
        """Return default metrics when calculation fails"""
        return {
            'reading_times': {
                'average': {'minutes': 0, 'seconds': 0, 'formatted': '0 sec'}
            },
            'word_metrics': {
                'word_count': 0,
                'sentence_count': 0,
                'paragraph_count': 0
            },
            'complexity_metrics': {
                'flesch_score': 50,
                'complexity_level': 'standard'
            },
            'difficulty_level': 'unknown',
            'error': 'Failed to calculate metrics'
        }

# Global instance
reading_metrics_calculator = ReadingMetricsCalculator()
