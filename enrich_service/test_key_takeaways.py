#!/usr/bin/env python3
"""
Test script for key_takeaways field validation in EnhancedSummarizationResponse
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models import EnhancedSummarizationResponse, NewsCategory, GeneralSubcategory, KeyTakeaway
from pydantic import ValidationError

def test_valid_key_takeaways():
    """Test valid key_takeaways field"""
    print("Testing valid key_takeaways...")
    
    try:
        response = EnhancedSummarizationResponse(
            summary="This is a test summary that contains exactly fifty words to meet the minimum requirement for the summary field validation. It discusses important news events and provides comprehensive coverage of the topic with proper journalistic tone and structure throughout.",
            haiku="Breaking news today\nImportant events unfold fast\nStory develops",
            key_takeaways=[
                KeyTakeaway(index=1, text="Major event occurred today."),
                KeyTakeaway(index=2, text="Three people were affected significantly."),
                KeyTakeaway(index=3, text="Investigation continues actively.")
            ],
            category=NewsCategory.GENERAL,
            subcategory=GeneralSubcategory.SOCIAL_ISSUES
        )
        print("✅ Valid key_takeaways passed validation")
        print(f"Key takeaways: {response.key_takeaways}")
        return True
    except ValidationError as e:
        print(f"❌ Valid key_takeaways failed: {e}")
        return False

def test_invalid_key_takeaways_count():
    """Test invalid key_takeaways count (not exactly 3)"""
    print("\nTesting invalid key_takeaways count...")
    
    try:
        response = EnhancedSummarizationResponse(
            summary="This is a test summary that contains exactly fifty words to meet the minimum requirement for the summary field validation. It discusses important news events and provides comprehensive coverage of the topic with proper journalistic tone and structure throughout.",
            haiku="Breaking news today\nImportant events unfold fast\nStory develops",
            key_takeaways=[
                KeyTakeaway(index=1, text="Major event occurred today."),
                KeyTakeaway(index=2, text="Investigation continues actively.")  # Only 2 items instead of 3
            ],
            category=NewsCategory.GENERAL,
            subcategory=GeneralSubcategory.SOCIAL_ISSUES
        )
        print("❌ Invalid count should have failed validation")
        return False
    except ValidationError as e:
        print(f"✅ Invalid count correctly failed: {e}")
        return True

def test_invalid_key_takeaways_word_count():
    """Test invalid key_takeaways word count (too long)"""
    print("\nTesting invalid key_takeaways word count...")
    
    try:
        response = EnhancedSummarizationResponse(
            summary="This is a test summary that contains exactly fifty words to meet the minimum requirement for the summary field validation. It discusses important news events and provides comprehensive coverage of the topic with proper journalistic tone and structure throughout.",
            haiku="Breaking news today\nImportant events unfold fast\nStory develops",
            key_takeaways=[
                KeyTakeaway(index=1, text="Major event occurred today."),
                KeyTakeaway(index=2, text="This is a very long sentence that exceeds the twelve word limit for validation purposes."),  # Too long
                KeyTakeaway(index=3, text="Investigation continues actively.")
            ],
            category=NewsCategory.GENERAL,
            subcategory=GeneralSubcategory.SOCIAL_ISSUES
        )
        print("❌ Invalid word count should have failed validation")
        return False
    except ValidationError as e:
        print(f"✅ Invalid word count correctly failed: {e}")
        return True

def test_invalid_key_takeaways_punctuation():
    """Test invalid key_takeaways punctuation (missing end punctuation)"""
    print("\nTesting invalid key_takeaways punctuation...")
    
    try:
        response = EnhancedSummarizationResponse(
            summary="This is a test summary that contains exactly fifty words to meet the minimum requirement for the summary field validation. It discusses important news events and provides comprehensive coverage of the topic with proper journalistic tone and structure throughout.",
            haiku="Breaking news today\nImportant events unfold fast\nStory develops",
            key_takeaways=[
                KeyTakeaway(index=1, text="Major event occurred today."),
                KeyTakeaway(index=2, text="Three people were affected significantly"), # Missing punctuation
                KeyTakeaway(index=3, text="Investigation continues actively.")
            ],
            category=NewsCategory.GENERAL,
            subcategory=GeneralSubcategory.SOCIAL_ISSUES
        )
        print("❌ Invalid punctuation should have failed validation")
        return False
    except ValidationError as e:
        print(f"✅ Invalid punctuation correctly failed: {e}")
        return True

def test_invalid_key_takeaways_duplicate_indices():
    """Test invalid key_takeaways with duplicate indices"""
    print("\nTesting invalid key_takeaways duplicate indices...")

    try:
        response = EnhancedSummarizationResponse(
            summary="This is a test summary that contains exactly fifty words to meet the minimum requirement for the summary field validation. It discusses important news events and provides comprehensive coverage of the topic with proper journalistic tone and structure throughout.",
            haiku="Breaking news today\nImportant events unfold fast\nStory develops",
            key_takeaways=[
                KeyTakeaway(index=1, text="Major event occurred today."),
                KeyTakeaway(index=1, text="Three people were affected significantly."), # Duplicate index
                KeyTakeaway(index=3, text="Investigation continues actively.")
            ],
            category=NewsCategory.GENERAL,
            subcategory=GeneralSubcategory.SOCIAL_ISSUES
        )
        print("❌ Duplicate indices should have failed validation")
        return False
    except ValidationError as e:
        print(f"✅ Duplicate indices correctly failed: {e}")
        return True

def main():
    """Run all tests"""
    print("Testing key_takeaways field validation...\n")
    
    tests = [
        test_valid_key_takeaways,
        test_invalid_key_takeaways_count,
        test_invalid_key_takeaways_word_count,
        test_invalid_key_takeaways_punctuation,
        test_invalid_key_takeaways_duplicate_indices
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n{'='*50}")
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
        return True
    else:
        print("❌ Some tests failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
