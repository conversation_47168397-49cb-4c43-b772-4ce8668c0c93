"""
Enhanced Summarization Models for Production LLM Service

This module contains Pydantic models for the enhanced news summarization service
using GPT-4.1-nano for fast, cost-effective article processing.
"""

from enum import Enum
from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field, field_validator
from datetime import datetime

class KeyTakeaway(BaseModel):
    """Individual key takeaway with explicit ordering."""
    index: int = Field(..., ge=1, le=3, description="Order index (1, 2, or 3)")
    text: str = Field(..., min_length=3, max_length=100, description="Takeaway text, max 8 words")

    @field_validator('text')
    @classmethod
    def validate_takeaway_text(cls, v):
        """Validate takeaway text format and word count."""
        word_count = len(v.split())
        if word_count > 12:  # Using 12 words for validation as requested
            raise ValueError(f"Takeaway too long: {word_count} words (max 12)")

        if word_count < 3:
            raise ValueError(f"Takeaway too short: {word_count} words (min 3)")

        if not v.strip().endswith(('.', '!', '?')):
            raise ValueError("Takeaway must end with complete sentences (., !, or ?)")

        return v

class NewsCategory(str, Enum):
    """Enumeration of valid news categories for classification."""
    POLITICS = "politics"
    BUSINESS = "business"
    TECHNOLOGY = "technology"
    SCIENCE = "science"
    HEALTH = "health"
    SPORTS = "sports"
    ENTERTAINMENT = "entertainment"
    LIFESTYLE = "lifestyle"
    WORLD_NEWS = "world news"
    HUMAN_INTEREST = "human interest"
    GENERAL = "general"

# Subcategory enums for each main category
class PoliticsSubcategory(str, Enum):
    """Politics subcategories."""
    ELECTIONS = "elections"
    LEGISLATION = "legislation"
    POLITICAL_PARTIES = "political parties"
    GOVERNMENT_AGENCIES = "government agencies"
    PUBLIC_POLICY = "public policy"

class BusinessSubcategory(str, Enum):
    """Business subcategories."""
    MARKETS = "markets"
    COMPANIES = "companies"
    PERSONAL_FINANCE = "personal finance"
    ECONOMICS = "economics"
    BIG_TECH = "big tech"
    START_UPS = "start ups"

class TechnologySubcategory(str, Enum):
    """Technology subcategories."""
    ARTIFICIAL_INTELLIGENCE = "artificial intelligence"
    SOFTWARE_APPS = "software & apps"
    HARDWARE = "hardware"
    SOCIAL_MEDIA = "social media"
    CYBERSECURITY = "cybersecurity"
    BLOCKCHAIN = "blockchain"
    CRYPTOCURRENCY = "cryptocurrency"
    VIRTUAL_REALITY = "virtual reality"
    AUGMENTED_REALITY = "augmented reality"
    QUANTUM_COMPUTING = "quantum computing"

class ScienceSubcategory(str, Enum):
    """Science subcategories."""
    ENVIRONMENT = "environment"
    SPACE_ASTRONOMY = "space & astronomy"
    BIOLOGY_MEDICINE = "biology & medicine"
    PHYSICS_CHEMISTRY = "physics & chemistry"
    ENVIRONMENT_CLIMATE_CHANGE = "environment & climate change"
    NATURE_WILDLIFE = "nature & wildlife"
    PHYSICS = "physics"
    CHEMISTRY = "chemistry"
    BIOLOGY = "biology"
    MEDICINE = "medicine"
    TECHNOLOGY = "technology"
    ENGINEERING = "engineering"
    MATH = "math"

class HealthSubcategory(str, Enum):
    """Health subcategories."""
    WELLNESS = "wellness"
    FITNESS = "fitness"
    NUTRITION = "nutrition"
    MENTAL_HEALTH = "mental health"
    PUBLIC_HEALTH = "public health"
    ALTERNATIVE_MEDICINE = "alternative medicine"
    HEALTHCARE = "healthcare"
    MEDICAL_RESEARCH = "medical research"
    MEDICAL_TECHNOLOGY = "medical technology"
    MEDICAL_DEVICES = "medical devices"
    MEDICAL_TREATMENTS = "medical treatments"
    MEDICAL_CONDITIONS = "medical conditions"
    MEDICAL_PROCEDURES = "medical procedures"

class SportsSubcategory(str, Enum):
    """Sports subcategories."""
    AMERICAN_FOOTBALL = "american football"
    BASKETBALL = "basketball"
    SOCCER = "soccer"
    TENNIS = "tennis"
    HOCKEY = "hockey"
    CRICKET = "cricket"
    ATHLETICS = "athletics"
    FANTASY_SPORTS = "fantasy sports"
    TOURNAMENTS = "tournaments"

class EntertainmentSubcategory(str, Enum):
    """Entertainment subcategories."""
    MOVIES = "movies"
    TV_SHOWS = "tv shows"
    MUSIC = "music"
    CELEBRITY_GOSSIP = "celebrity gossip"
    AWARDS = "awards"
    EVENTS = "events"
    MUSIC_FESTIVALS = "music festivals"
    MUSIC_CHARTS = "music charts"
    PERFORMING_ARTS = "performing arts"

class LifestyleSubcategory(str, Enum):
    """Lifestyle subcategories."""
    TRAVEL = "travel"
    PETS = "pets"
    FOOD = "food"
    FASHION = "fashion"
    OUTDOORS = "outdoors"
    PHOTOGRAPHY = "photography"
    BEAUTY = "beauty"
    BOOKS = "books"
    HOME = "home"
    GARDENING = "gardening"
    RELATIONSHIPS = "relationships"
    FAMILY = "family"
    HOBBIES = "hobbies"
    GAMING = "gaming"
    VLOGGING = "vlogging"
    VISUAL_ART = "visual art"
    CULTURE = "culture"

class WorldNewsSubcategory(str, Enum):
    """World news subcategories."""
    WAR_CONFLICT = "war & conflict"
    TRADE = "trade"
    DIPLOMACY = "diplomacy"
    NATURAL_DISASTERS = "natural disasters"
    INTERNATIONAL_RELATIONS = "international relations"
    IMMIGRATION = "immigration"
    HUMAN_RIGHTS = "human rights"
    REFUGEES = "refugees"
    GLOBAL_ECONOMY = "global economy"
    GLOBAL_HEALTH = "global health"
    GLOBAL_ENVIRONMENT = "global environment"
    GLOBAL_SECURITY = "global security"
    GLOBAL_CULTURE = "global culture"
    POVERTY = "poverty"
    INTERNATIONAL_DEVELOPMENT = "international development"
    INTERNATIONAL_ORGANIZATIONS = "international organizations"

class HumanInterestSubcategory(str, Enum):
    """Human interest subcategories."""
    QUIRKY = "quirky"
    OFFBEAT = "offbeat"
    FEEL_GOOD = "feel good"
    FUNNY = "funny"
    TRIUMPH = "triumph"
    HEROIC_ACTS = "heroic acts"
    INSPIRING = "inspiring"

class GeneralSubcategory(str, Enum):
    """General subcategories."""
    CAREERS = "careers"
    WORKPLACE = "workplace"
    COMMUNITY = "community"
    SOCIAL_SERVICES = "social services"
    CRIME = "crime"
    LAW_ENFORCEMENT = "law enforcement"
    HISTORY = "history"
    DEVELOPMENT = "development"
    INFRASTRUCTURE = "infrastructure"
    ENERGY = "energy"
    DISASTERS = "disasters"
    ACCIDENTS = "accidents"
    EDUCATION = "education"
    ENVIRONMENT = "environment"
    CONSERVATION = "conservation"
    EVENTS_HOLIDAYS = "events & holidays"
    ROYALTY = "royalty"
    REMEMBRANCE = "remembrance"
    URBAN_ISSUES = "urban issues"
    IMMIGRATION_MIGRATION = "immigration & migration"
    INCIDENTS_RESPONSE = "incidents & response"
    JUSTICE_LEGAL = "justice & legal"
    MEDIA_NEWS = "media & news"
    NATURE = "nature"
    EXPLORATION = "exploration"
    RELIGION = "religion"
    SOCIAL_ISSUES = "social issues"
    WEATHER = "weather"

# Union type for all subcategories
NewsSubcategory = Union[
    PoliticsSubcategory,
    BusinessSubcategory,
    TechnologySubcategory,
    ScienceSubcategory,
    HealthSubcategory,
    SportsSubcategory,
    EntertainmentSubcategory,
    LifestyleSubcategory,
    WorldNewsSubcategory,
    HumanInterestSubcategory,
    GeneralSubcategory
]

class EnhancedSummarizationResponse(BaseModel):
    """Pydantic model for enhanced summarization response with validation."""
    summary: str = Field(
        ...,
        min_length=50,
        max_length=800,
        description="100-word summary of the article"
    )
    haiku: str = Field(
        ...,
        min_length=10,
        max_length=100,
        description="Haiku summary with 5-7-5 syllable structure"
    )
    key_takeaways: List[KeyTakeaway] = Field(
        ...,
        min_length=3,
        max_length=3,
        description="Three key takeaway sentences with explicit ordering"
    )
    category: NewsCategory = Field(
        ...,
        description="Primary category from the news_categories list"
    )
    subcategory: NewsSubcategory = Field(
        ...,
        description="Primary subcategory under the chosen category"
    )

    @field_validator('summary')
    @classmethod
    def validate_summary_word_count(cls, v):
        word_count = len(v.split())
        if word_count > 100:
            raise ValueError(f"Summary too long: {word_count} words (max 100)")
        if not v.strip().endswith(('.', '!', '?')):
            raise ValueError("Summary must end with complete sentences (., !, or ?)")

        # Check for forbidden phrases that reference the article
        forbidden_phrases = [
            "the article discusses",
            "this piece talks about",
            "the article talks about",
            "this article discusses",
            "the piece discusses",
            "according to the article",
            "the article states",
            "the article mentions"
        ]

        summary_lower = v.lower()
        for phrase in forbidden_phrases:
            if phrase in summary_lower:
                raise ValueError(f"Summary must not reference the article directly. Avoid phrases like '{phrase}'. Present information as factual statements.")

        return v

    @field_validator('key_takeaways')
    @classmethod
    def validate_key_takeaways(cls, v):
        """Validate key takeaways structure and ordering."""
        if not isinstance(v, list):
            raise ValueError("key_takeaways must be a list")

        if len(v) != 3:
            raise ValueError(f"key_takeaways must contain exactly 3 items (got {len(v)})")

        # Check that we have exactly indices 1, 2, 3
        indices = [takeaway.index for takeaway in v]
        expected_indices = [1, 2, 3]

        if sorted(indices) != expected_indices:
            raise ValueError(f"key_takeaways must have indices [1, 2, 3], got {sorted(indices)}")

        # Check for duplicate indices
        if len(set(indices)) != len(indices):
            raise ValueError("key_takeaways cannot have duplicate indices")

        return v

    @field_validator('subcategory')
    @classmethod
    def validate_subcategory_matches_category(cls, v, info):
        """Validate that subcategory matches the selected category."""
        if 'category' not in info.data:
            return v  # Category not set yet, will be validated later

        category = info.data['category']
        subcategory_value = v.value if hasattr(v, 'value') else str(v)

        # Define valid subcategories for each category
        valid_subcategories = {
            NewsCategory.POLITICS: [s.value for s in PoliticsSubcategory],
            NewsCategory.BUSINESS: [s.value for s in BusinessSubcategory],
            NewsCategory.TECHNOLOGY: [s.value for s in TechnologySubcategory],
            NewsCategory.SCIENCE: [s.value for s in ScienceSubcategory],
            NewsCategory.HEALTH: [s.value for s in HealthSubcategory],
            NewsCategory.SPORTS: [s.value for s in SportsSubcategory],
            NewsCategory.ENTERTAINMENT: [s.value for s in EntertainmentSubcategory],
            NewsCategory.LIFESTYLE: [s.value for s in LifestyleSubcategory],
            NewsCategory.WORLD_NEWS: [s.value for s in WorldNewsSubcategory],
            NewsCategory.HUMAN_INTEREST: [s.value for s in HumanInterestSubcategory],
            NewsCategory.GENERAL: [s.value for s in GeneralSubcategory],
        }

        if subcategory_value not in valid_subcategories.get(category, []):
            valid_options = ', '.join(valid_subcategories.get(category, []))
            raise ValueError(f"Subcategory '{subcategory_value}' is not valid for category '{category.value}'. Valid options: {valid_options}")

        return v

class OriginalArticleData(BaseModel):
    """Model for original article data from GCS."""
    uri: str = Field(..., description="Article unique identifier")
    title: str = Field(..., description="Article title")
    body: str = Field(..., description="Article body content")
    source_title: Optional[str] = Field(None, description="Source publication name")
    original_categories: List[str] = Field(default=[], description="Original categories from source")
    concepts: List[Dict[str, Any]] = Field(default=[], description="Original concepts/topics from EventRegistry")
    concept_labels: List[str] = Field(default=[], description="Extracted concept labels in English")
    source_file: Optional[str] = Field(None, description="GCS source file path")

class EnhancedArticleResult(BaseModel):
    """Complete result combining original data with LLM enhancements."""
    # Original data
    uri: str
    title: str
    original_category: Optional[str] = None
    original_concept_labels: List[str] = Field(default=[], description="Original concept labels in English")
    source_title: Optional[str] = None
    
    # LLM generated fields
    summary: str
    haiku: str
    key_takeaways: List[KeyTakeaway] = Field(default=[], description="Three key takeaway sentences with explicit ordering")
    llm_category: str
    llm_subcategory: str

    # Reading metrics fields
    estimated_read_time: Optional[str] = Field(None, description="Formatted reading time (e.g., '2 min')")
    read_time_seconds: Optional[int] = Field(None, description="Reading time in seconds")
    reading_difficulty: Optional[str] = Field(None, description="Reading difficulty level")
    reading_grade_level: Optional[str] = Field(None, description="Grade level for reading comprehension")
    suitable_for_general_audience: Optional[bool] = Field(None, description="Whether suitable for general audience")
    reading_metrics: Optional[Dict[str, Any]] = Field(None, description="Complete reading metrics data")

    # Metadata
    processing_time: Optional[float] = None
    timestamp: datetime = Field(default_factory=datetime.now)
    error: Optional[str] = None

class BatchProcessingResult(BaseModel):
    """Result of batch processing operation."""
    total_articles: int
    successful_articles: int
    failed_articles: int
    success_rate: float
    average_processing_time: float
    total_processing_time: float
    results: List[EnhancedArticleResult]
    errors: List[str] = Field(default=[])
    
    def calculate_metrics(self):
        """Calculate processing metrics."""
        self.success_rate = (self.successful_articles / self.total_articles) * 100 if self.total_articles > 0 else 0
        if self.successful_articles > 0:
            successful_times = [r.processing_time for r in self.results if r.processing_time and not r.error]
            self.average_processing_time = sum(successful_times) / len(successful_times) if successful_times else 0

# Breaking News Analysis Models removed - handled in transform service

class CostAnalysis(BaseModel):
    """Cost analysis for LLM processing."""
    estimated_input_tokens: int
    estimated_output_tokens: int
    estimated_cost: float
    cost_per_1000_articles: float
    model_used: str

    @classmethod
    def calculate_cost(cls, articles_count: int, avg_input_tokens: int = 4200, avg_output_tokens: int = 170, model: str = "gpt-4.1-nano"):
        """Calculate cost analysis for given parameters."""
        # GPT-4.1-nano pricing (same as GPT-4o-mini)
        input_cost_per_1m = 0.15
        output_cost_per_1m = 0.60

# HTTP API Models
class ArticleEnrichmentRequest(BaseModel):
    """Request model for single article enrichment."""
    article: Dict[str, Any] = Field(..., description="Article data to enrich")
    include_summary: bool = Field(default=True, description="Include LLM summary")
    include_categorization: bool = Field(default=True, description="Include LLM categorization")
    include_breaking_news: bool = Field(default=True, description="Include breaking news analysis")

class ArticleEnrichmentResponse(BaseModel):
    """Response model for single article enrichment."""
    status: str = Field(..., description="Processing status")
    article: Dict[str, Any] = Field(..., description="Enriched article data")
    processing_time_ms: Optional[int] = Field(None, description="Processing time in milliseconds")
    timestamp: datetime = Field(default_factory=datetime.now)

class BatchEnrichmentRequest(BaseModel):
    """Request model for batch article enrichment."""
    articles: List[Dict[str, Any]] = Field(..., description="List of articles to enrich")
    include_summary: bool = Field(default=True, description="Include LLM summary")
    include_categorization: bool = Field(default=True, description="Include LLM categorization")
    include_breaking_news: bool = Field(default=True, description="Include breaking news analysis")
    max_concurrent: int = Field(default=5, description="Maximum concurrent processing")
