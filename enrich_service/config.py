"""
Configuration settings for ML Article Classification Service
"""

import os
from typing import List, Dict

# Custom Categories for Classification
CUSTOM_CATEGORIES = [
    "Politics",
    "Business", 
    "Technology",
    "Science",
    "Wellness",
    "Health",
    "Sports",
    "Entertainment",
    "Lifestyle",
    "Human Interest",
    "World News",
    "AI"
]

# Category Definitions for LLM Labeling
CATEGORY_DEFINITIONS = {
    "Politics": ["elections", "legislation", "political parties", "government agencies"],
    "Business": ["Markets", "Companies", "Personal Finance", "Finance", "Investment", "Economics", "Big Tech", "Start Ups"],
    "Technology": ["Artificial Intelligence (AI)", "Software & Apps", "Software", "Hardware", "Social Media", "Internet", "Gadgets", "Cybersecurity"],
    "Science": ["Environment", "Space & Astronomy", "Biology", "Physics", "Chemistry", "Research", "Climate Change", "Nature", "Wildlife"],
    "Wellness": ["Wellness", "Fitness", "Nutrition", "Weight Loss", "Exercise", "Mental Health", "Sleep Science", "Self-Care", "Personal Growth"],
    "Health": ["Public Health", "Alternative Medicine", "Health Policy", "Big Pharma"],
    "Sports": ["American Football", "Football", "Basketball", "Baseball", "Soccer", "Tennis", "Hockey", "Athletics", "Tournaments"],
    "Entertainment": ["Movies", "TV Shows", "Music", "Celebrity News & Gossip", "Performing Arts"],
    "Lifestyle": ["Visual Arts", "Crafts", "Pets", "Food", "Fashion", "Beauty", "Home", "Gardening", "Family", "Hobbies", "Gaming", "Vlogging", "Travel"],
    "Human Interest": ["Quirky", "Offbeat", "Funny", "Acts of Kindness", "Resilience and Triumph", "Inspiring Innovations"],
    "World News": ["International Politics", "Global Economy", "War and Conflict", "International Trade", "Foreign affairs", "International relations", "diplomacy"],
    "AI": ["LLMs", "Agentic AI", "Robotics", "Generative AI", "AI Apps", "AI Economy"]
}

# Model Configuration
MODEL_CONFIG = {
    "target_accuracy": 0.95,
    "articles_per_category": 4000,
    "total_training_articles": 48000,
    "train_ratio": 0.7,
    "val_ratio": 0.15,
    "test_ratio": 0.15,
    "batch_size": 100,
    "max_features": 10000,
    "ngram_range": (1, 2)
}

# LLM Configuration with Updated 2024 Pricing (per 1M tokens)
LLM_CONFIG = {
    # Primary model (balanced performance/cost)
    'model': 'gpt-4.1-nano',  # Fastest option with same cost as mini
    'provider': 'openai',
    'primary_model': 'gpt-4.1-nano',  # Backup compatibility
    'primary_provider': 'openai',
    
    # Alternative models ordered by cost-effectiveness
    'models': {
        # MOST COST-EFFECTIVE OPTIONS
        'gpt-4.1-nano': {
            'provider': 'openai',
            'input_cost_per_1m_tokens': 0.15,   # $0.15 (same as mini)
            'output_cost_per_1m_tokens': 0.60,  # $0.60 (same as mini)
            'batch_input_cost_per_1m_tokens': 0.075,   # 50% discount
            'batch_output_cost_per_1m_tokens': 0.30,   # 50% discount
            'context_limit': 128000,
            'recommended_for': 'Fastest processing, optimal for real-time news enrichment',
            'performance_tier': 'high-speed'
        },
        'gpt-4o-mini': {
            'provider': 'openai',
            'input_cost_per_1m_tokens': 0.15,   # $0.15
            'output_cost_per_1m_tokens': 0.60,  # $0.60
            'batch_input_cost_per_1m_tokens': 0.075,   # 50% discount
            'batch_output_cost_per_1m_tokens': 0.30,   # 50% discount
            'context_limit': 128000,
            'recommended_for': 'High-volume classification, cost-sensitive applications',
            'performance_tier': 'high-quality'
        },
        'claude-3-haiku': {
            'provider': 'anthropic',
            'input_cost_per_1m_tokens': 0.25,   # $0.25
            'output_cost_per_1m_tokens': 1.25,  # $1.25
            'batch_input_cost_per_1m_tokens': 0.125,  # 50% discount
            'batch_output_cost_per_1m_tokens': 0.625,  # 50% discount
            'context_limit': 200000,
            'recommended_for': 'Fast responses, simple classification tasks',
            'performance_tier': 'high-speed'
        },
        
        # BALANCED OPTIONS
        'claude-3.5-haiku': {
            'provider': 'anthropic',
            'input_cost_per_1m_tokens': 0.80,   # $0.80
            'output_cost_per_1m_tokens': 4.00,  # $4.00
            'batch_input_cost_per_1m_tokens': 0.40,   # 50% discount
            'batch_output_cost_per_1m_tokens': 2.00,   # 50% discount
            'context_limit': 200000,
            'recommended_for': 'Modern performance with reasonable cost',
            'performance_tier': 'high-quality'
        },
        'gpt-4o': {
            'provider': 'openai',
            'input_cost_per_1m_tokens': 2.50,   # $2.50
            'output_cost_per_1m_tokens': 10.00, # $10.00
            'batch_input_cost_per_1m_tokens': 1.25,   # 50% discount
            'batch_output_cost_per_1m_tokens': 5.00,   # 50% discount
            'context_limit': 128000,
            'recommended_for': 'Complex reasoning, high accuracy requirements',
            'performance_tier': 'premium'
        },
        
        # HIGH-PERFORMANCE OPTIONS
        'claude-3.5-sonnet': {
            'provider': 'anthropic',
            'input_cost_per_1m_tokens': 3.00,   # $3.00
            'output_cost_per_1m_tokens': 15.00, # $15.00
            'batch_input_cost_per_1m_tokens': 1.50,   # 50% discount
            'batch_output_cost_per_1m_tokens': 7.50,   # 50% discount
            'context_limit': 200000,
            'recommended_for': 'High intelligence, complex analysis',
            'performance_tier': 'premium'
        },
        'claude-4-sonnet': {
            'provider': 'anthropic',
            'input_cost_per_1m_tokens': 3.00,   # $3.00
            'output_cost_per_1m_tokens': 15.00, # $15.00
            'batch_input_cost_per_1m_tokens': 1.50,   # 50% discount
            'batch_output_cost_per_1m_tokens': 7.50,   # 50% discount
            'context_limit': 200000,
            'recommended_for': 'Latest model, best performance',
            'performance_tier': 'premium'
        },
        'gpt-3.5-turbo': {
            'provider': 'openai',
            'input_cost_per_1m_tokens': 3.00,   # $3.00
            'output_cost_per_1m_tokens': 6.00,  # $6.00
            'batch_input_cost_per_1m_tokens': 1.50,   # 50% discount
            'batch_output_cost_per_1m_tokens': 3.00,   # 50% discount
            'context_limit': 16000,
            'recommended_for': 'Proven performance, lower output cost than Claude',
            'performance_tier': 'balanced'
        }
    },
    
    # Cost estimation settings
    'batch_size': 10,
    'max_tokens': 500,  # Conservative estimate for article classification
    'temperature': 0.1,
    'use_batch_api': True,  # Enable 50% cost savings for large jobs
    
    # Retry configuration
    'max_retries': 3,
    'retry_attempts': 3,  # Compatibility alias
    'retry_delay': 1.0
}

# API Keys - with GCP Secrets Manager integration
try:
    from gcp_secrets import setup_openai_key
    OPENAI_API_KEY = setup_openai_key()
except ImportError:
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")

ANTHROPIC_API_KEY = os.getenv("ANTHROPIC_API_KEY")

# GCS Configuration
GCS_CONFIG = {
    "bucket_name": os.getenv("GCS_BUCKET", "newsreporter"),
    "models_folder": "ml_models",
    "datasets_folder": "ml_datasets",
    "logs_folder": "ml_logs"
}

# Source Image Configuration
SOURCE_IMAGE_CONFIG = {
    "logo_dev_token": os.getenv("LOGO_DEV_TOKEN", "pk_dhMaVdjySqiMtC3Loca6xQ"),
    "image_size": 200,
    "image_format": "png"
}

# Service Configuration
SERVICE_CONFIG = {
    "host": "0.0.0.0",
    "port": int(os.getenv("PORT", 8080)),
    "debug": os.getenv("DEBUG", "false").lower() == "true",
    "max_batch_size": 5000,
    "timeout": 300
}

# Paths
DATA_DIR = "data"
MODELS_DIR = "models"
LOGS_DIR = "logs"

# Create directories if they don't exist
os.makedirs(DATA_DIR, exist_ok=True)
os.makedirs(MODELS_DIR, exist_ok=True)
os.makedirs(LOGS_DIR, exist_ok=True)

def ensure_directories():
    """Create necessary directories if they don't exist."""
    directories = [DATA_DIR, MODELS_DIR, LOGS_DIR]
    for directory in directories:
        os.makedirs(directory, exist_ok=True)

# Enhanced Summarization Prompt (balanced simplicity and effectiveness)
SUMMARIZATION_PROMPT = """Analyze this news article and provide:

1. **Summary**: Write a concise 80-word summary capturing the key facts and main points. Maintain a neutral, objective, and journalistic tone. Avoid phrases like "The article discusses," "This piece talks about," or directly referencing the article itself. Instead, present the information as factual statements of events and developments. The summary must be complete sentences and not exceed 80 words total.
2. **Haiku**: Create a haiku (5-7-5 syllable structure) that captures the essence of the story
3. **Key Takeaways**: Provide exactly 3 key takeaway sentences that summarize the most important points. Each sentence should be 8 words or fewer and end with proper punctuation. Focus on the core facts, outcomes, or implications that readers should remember. Format as an array of objects with "index" (1, 2, or 3) and "text" fields.
4. **Category**: Choose the most appropriate primary category from the available options
5. **Subcategory**: Select the most specific subcategory that fits under your chosen category

**Available Categories and Subcategories:**

• **politics**: elections, legislation, political parties, government agencies, public policy
• **business**: markets, companies, personal finance, finance, investment, economics, big tech, start ups
• **technology**: artificial intelligence, software & apps, software, hardware, social media, internet, cybersecurity, blockchain, cryptocurrency, virtual reality, augmented reality, quantum computing
• **science**: environment, space & astronomy, biology & medicine, physics & chemistry, environment & climate change, nature & wildlife, physics, chemistry, biology, medicine, research, technology, engineering, math
• **health**: wellness, fitness, nutrition, mental health, public health, alternative medicine, healthcare, medical research, medical technology, medical devices, medical treatments, medical conditions, medical procedures
• **sports**: american football, football, basketball, baseball, soccer, tennis, hockey, cricket, athletics, fantasy sports, tournaments
• **entertainment**: movies, tv shows, music, celebrity gossip, awards, events, music festivals, music charts, performing arts
• **lifestyle**: travel, pets, food, fashion, outdoors, photography, beauty, books, home, gardening, relationships, family, hobbies, gaming, vlogging, visual art, culture
• **world news**: war & conflict, trade, diplomacy, natural disasters, international relations, immigration, human rights, refugees, global economy, global health, global environment, global security, global culture, poverty, international development, international organizations
• **human interest**: quirky, offbeat, feel good, funny, triumph, heroic acts, inspiring
• **general**: careers, workplace, community, social services, crime, law enforcement, history, development, infrastructure, energy, disasters, accidents, education, environment, conservation, events & holidays, royalty, remembrance, urban issues, immigration & migration, incidents & response, justice & legal, media & news, nature, exploration, religion, social issues, weather

It is ABSOLUTELY IMPORTANT that you assign subcategories to their proper categories. Failure to do so will result in validation failures, time wasting, and additional costs.


**Important Guidelines:**
• You must try to find the most appropriate category and subcategory from the lists above
• Categories and subcategories must come EXACTLY from the provided lists
• Match subcategories to their correct parent category (e.g., "social issues" goes with "general", not "human interest")
• If you cannot find a suitable category, use the "General" category
• The General category is only a last resort. Check and double check carefully to see if an article belongs in one of the main categories
• When in doubt, choose the broader category rather than forcing a poor fit

**Response Format:**
```json
{
  "summary": "Your 100-word summary here...",
  "haiku": "Your haiku here...",
  "key_takeaways": [
    {"index": 1, "text": "First key takeaway sentence."},
    {"index": 2, "text": "Second key takeaway sentence."},
    {"index": 3, "text": "Third key takeaway sentence."}
  ],
  "category": "category_name",
  "subcategory": "subcategory_name"
}
```

Article:"""

# News Categories for Classification
NEWS_CATEGORIES = {
    "politics": ["elections", "legislation", "political parties", "government agencies", "public policy"],
    "business": ["markets", "companies", "personal finance", "finance", "investment", "economics", "big tech", "start ups"],
    "technology": ["artificial intelligence", "software & apps", "software", "hardware", "social media", "internet", "cybersecurity", "blockchain", "cryptocurrency", "virtual reality", "augmented reality", "quantum computing"],
    "science": ["environment", "space & astronomy", "biology & medicine", "physics & chemistry", "environment & climate change", "nature & wildlife", "physics", "chemistry", "biology", "medicine", "research", "technology", "engineering", "math"],
    "health": ["wellness", "fitness", "nutrition", "mental health", "public health", "alternative medicine", "healthcare", "medical research", "medical technology", "medical devices", "medical treatments", "medical conditions", "medical procedures"],
    "sports": ["american football", "football", "basketball", "baseball", "soccer", "tennis", "hockey", "cricket", "athletics", "fantasy sports", "tournaments"],
    "entertainment": ["movies", "TV Shows", "music", "celebrity gossip", "awards", "events", "music festivals", "music charts", "performing arts"],
    "lifestyle": ["travel", "pets", "food", "fashion", "outdoors", "photography", "beauty", "books", "home", "gardening", "relationships", "family", "hobbies", "gaming", "vlogging", "visual art", "culture"],
    "world news": ["war & conflict", "trade", "diplomacy", "natural disasters", "international relations", "immigration", "human rights", "refugees", "global economy", "global health", "global environment", "global security", "global culture", "poverty", "international development", "international organizations"],
    "human interest": ["quirky", "offbeat", "feel good", "funny", "triumph", "heroic acts", "inspiring"],
    "general": ["careers", "workplace", "community", "social services", "crime", "law enforcement", "history", "development", "infrastructure", "energy", "disasters", "accidents", "education", "environment", "conservation", "events & holidays", "royalty", "remembrance", "urban issues", "immigration & migration", "incidents & response", "justice & legal", "media & news", "nature", "exploration", "religion", "social issues", "weather"]
}

