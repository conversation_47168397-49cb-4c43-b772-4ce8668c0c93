"""
Dynamic Category Configuration System
Allows runtime updates to categories, subcategories, and keyword mappings
"""

import json
import os
from typing import Dict, List, Any
# Using REST client instead of grpcio-based firestore
import sys
sys.path.append('../shared_utils')
try:
    from gcp_rest_client import FirestoreRestClient
except ImportError:
    FirestoreRestClient = None
import logging

logger = logging.getLogger(__name__)

class CategoryConfigManager:
    """Manages dynamic category configuration from Firestore"""
    
    def __init__(self):
        if FirestoreRestClient:
            self.firestore_db = FirestoreRestClient()
        else:
            self.firestore_db = None
            logger.warning("Firestore REST client not available, using fallback configuration")
        self.config_collection = "category_config"
        self._cache = {}
        self._cache_timestamp = None
        
    def get_category_config(self) -> Dict[str, Any]:
        """Get current category configuration with caching"""
        try:
            # Check cache (5 minute TTL)
            import time
            current_time = time.time()
            if (self._cache_timestamp and 
                current_time - self._cache_timestamp < 300 and 
                self._cache):
                return self._cache
            
            # Fetch from Firestore using REST client
            config_data = self.firestore_db.get_document(self.config_collection, "main")

            if config_data:
                self._cache = config_data
                self._cache_timestamp = current_time
                logger.info("Loaded category configuration from Firestore")
                return config_data
            else:
                # Create default configuration
                default_config = self._get_default_config()
                self._save_config(default_config)
                self._cache = default_config
                self._cache_timestamp = current_time
                logger.info("Created default category configuration")
                return default_config
                
        except Exception as e:
            logger.error(f"Failed to load category configuration: {e}")
            # Fallback to default
            return self._get_default_config()
    
    def update_category_config(self, config: Dict[str, Any]) -> bool:
        """Update category configuration in Firestore"""
        try:
            success = self.firestore_db.set_document(self.config_collection, "main", config)
            if success:
                # Clear cache
                self._cache = {}
                self._cache_timestamp = None
                logger.info("Updated category configuration in Firestore")
                return True
            else:
                logger.error("Failed to update category configuration in Firestore")
                return False
        except Exception as e:
            logger.error(f"Failed to update category configuration: {e}")
            return False
    
    def add_category(self, category_name: str, subcategories: List[str] = None) -> bool:
        """Add a new category with optional subcategories"""
        config = self.get_category_config()
        
        category_id = category_name.lower().replace(' ', '_')
        config['categories'][category_id] = {
            'name': category_name,
            'subcategories': subcategories or ['general']
        }
        
        # Add subcategory mappings
        if category_name not in config['subcategory_mappings']:
            config['subcategory_mappings'][category_name] = {
                'general': ['general', 'other', 'misc']
            }
        
        return self.update_category_config(config)
    
    def add_subcategory(self, category_name: str, subcategory_name: str, keywords: List[str]) -> bool:
        """Add a new subcategory to an existing category"""
        config = self.get_category_config()
        
        if category_name in config['subcategory_mappings']:
            config['subcategory_mappings'][category_name][subcategory_name] = keywords
            
            # Add to category subcategories list
            category_id = category_name.lower().replace(' ', '_')
            if category_id in config['categories']:
                if subcategory_name not in config['categories'][category_id]['subcategories']:
                    config['categories'][category_id]['subcategories'].append(subcategory_name)
            
            return self.update_category_config(config)
        
        return False
    
    def get_subcategory_for_topic(self, category_name: str, topic_name: str) -> str:
        """Determine subcategory for a topic based on current configuration"""
        config = self.get_category_config()
        topic_lower = topic_name.lower()
        
        if category_name in config['subcategory_mappings']:
            for subcategory, keywords in config['subcategory_mappings'][category_name].items():
                for keyword in keywords:
                    if keyword.lower() in topic_lower:
                        return subcategory
        
        return 'general'
    
    def _save_config(self, config: Dict[str, Any]):
        """Save configuration to Firestore"""
        try:
            self.firestore_db.set_document(self.config_collection, "main", config)
        except Exception as e:
            logger.error(f"Failed to save configuration: {e}")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default category configuration"""
        return {
            "categories": {
                "politics": {"name": "Politics", "subcategories": ["elections", "legislation", "political_parties", "government_agencies", "public_policy"]},
                "business": {"name": "Business", "subcategories": ["markets", "companies", "personal_finance", "finance", "investment", "economics", "big_tech", "start_ups"]},
                "technology": {"name": "Technology", "subcategories": ["artificial_intelligence", "software_apps", "software", "hardware", "social_media", "internet", "gadgets", "cybersecurity"]},
                "science": {"name": "Science", "subcategories": ["environment", "space_astronomy", "biology_medicine", "physics_chemistry", "research", "nature_wildlife"]},
                "sports": {"name": "Sports", "subcategories": ["american_football", "football", "basketball", "baseball", "soccer", "tennis", "hockey", "athletics"]},
                "entertainment": {"name": "Entertainment", "subcategories": ["movies", "tv_shows", "music", "celebrity_gossip", "awards", "performing_arts"]},
                "health": {"name": "Health", "subcategories": ["public_health", "wellness", "mental_health", "medical_research"]},
                "lifestyle": {"name": "Lifestyle", "subcategories": ["fashion", "food_cooking", "travel", "home_garden"]},
                "world_news": {"name": "World News", "subcategories": ["international_relations", "conflicts", "global_events"]},
                "human_interest": {"name": "Human Interest", "subcategories": ["community", "inspiring_stories", "social_issues"]}
            },
            "subcategory_mappings": {
                "Politics": {
                    "elections": ["election", "vote", "campaign", "candidate", "ballot"],
                    "legislation": ["law", "bill", "congress", "senate", "parliament", "policy"],
                    "political_parties": ["democrat", "republican", "party", "political"],
                    "government_agencies": ["government", "agency", "department", "federal"],
                    "public_policy": ["policy", "regulation", "reform", "initiative"]
                },
                "Business": {
                    "markets": ["market", "stock", "trading", "exchange", "dow", "nasdaq"],
                    "companies": ["company", "corporation", "business", "firm", "enterprise"],
                    "personal_finance": ["personal finance", "money", "savings", "retirement", "budget"],
                    "finance": ["finance", "financial", "banking", "credit", "loan"],
                    "investment": ["investment", "investing", "portfolio", "fund", "asset"],
                    "economics": ["economy", "economic", "gdp", "inflation", "recession"],
                    "big_tech": ["apple", "google", "microsoft", "amazon", "meta", "tesla"],
                    "start_ups": ["startup", "venture", "funding", "entrepreneur"]
                },
                "Technology": {
                    "artificial_intelligence": ["ai", "artificial intelligence", "machine learning", "neural"],
                    "software_apps": ["software & apps", "app", "application", "program", "mobile app"],
                    "software": ["software", "code", "programming", "development", "platform"],
                    "hardware": ["hardware", "chip", "processor", "device", "computer"],
                    "social_media": ["social", "facebook", "twitter", "instagram", "tiktok"],
                    "internet": ["internet", "web", "online", "digital", "connectivity"],
                    "gadgets": ["gadget", "phone", "smartphone", "tablet", "laptop"],
                    "cybersecurity": ["cyber", "security", "hack", "breach", "privacy"]
                },
                "Science": {
                    "environment": ["environment", "climate", "pollution", "carbon", "green"],
                    "space_astronomy": ["space", "nasa", "astronomy", "planet", "satellite"],
                    "biology_medicine": ["biology", "medical", "health", "disease", "medicine"],
                    "physics_chemistry": ["physics", "chemistry", "quantum", "molecule"],
                    "research": ["research", "study", "experiment", "scientific", "discovery"],
                    "nature_wildlife": ["nature", "wildlife", "animal", "species", "conservation"]
                },
                "Sports": {
                    "american_football": ["american football", "nfl", "quarterback", "touchdown"],
                    "football": ["football", "gridiron", "super bowl", "college football"],
                    "basketball": ["basketball", "nba", "court", "dunk", "playoff"],
                    "baseball": ["baseball", "mlb", "pitcher", "home run", "world series"],
                    "soccer": ["soccer", "fifa", "world cup", "goal", "premier league"],
                    "tennis": ["tennis", "wimbledon", "court", "serve", "match"],
                    "hockey": ["hockey", "nhl", "ice", "puck", "goal"],
                    "athletics": ["track", "field", "marathon", "olympics", "athlete"]
                },
                "Entertainment": {
                    "movies": ["movie", "film", "cinema", "actor", "director", "oscar"],
                    "tv_shows": ["tv", "television", "series", "show", "episode"],
                    "music": ["music", "song", "album", "artist", "concert", "grammy"],
                    "celebrity_gossip": ["celebrity", "gossip", "scandal", "relationship"],
                    "awards": ["award", "oscar", "grammy", "emmy", "golden globe"],
                    "performing_arts": ["theater", "theatre", "dance", "ballet", "opera", "broadway", "performance", "stage"]
                },
                "Health": {
                    "public_health": ["public health", "epidemic", "vaccine", "healthcare"],
                    "wellness": ["wellness", "fitness", "nutrition", "diet", "exercise"],
                    "mental_health": ["mental", "depression", "anxiety", "therapy"],
                    "medical_research": ["research", "study", "clinical", "trial", "medicine"]
                },
                "Lifestyle": {
                    "fashion": ["fashion", "style", "clothing", "designer", "trend"],
                    "food_cooking": ["food", "cooking", "recipe", "restaurant", "chef"],
                    "travel": ["travel", "vacation", "tourism", "destination", "hotel"],
                    "home_garden": ["home", "garden", "interior", "design", "diy"]
                },
                "World News": {
                    "international_relations": ["diplomatic", "embassy", "foreign", "international"],
                    "conflicts": ["war", "conflict", "military", "peace", "treaty"],
                    "global_events": ["global", "world", "international", "summit", "conference"]
                },
                "Human Interest": {
                    "community": ["community", "local", "neighborhood", "volunteer"],
                    "inspiring_stories": ["inspiring", "hero", "rescue", "heartwarming"],
                    "social_issues": ["social", "justice", "rights", "equality", "activism"]
                }
            },
            "last_updated": "2024-01-15T10:30:00Z",
            "version": "1.0"
        }

# Global instance
category_config_manager = CategoryConfigManager()
