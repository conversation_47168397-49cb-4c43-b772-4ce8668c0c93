# Enriched Article Schema

## Overview

The Enrich Service now produces fully enriched articles with LLM-based categorization, breaking news analysis, reading metrics, and enhanced metadata. This document defines the complete schema for enriched articles.

## Complete Enriched Article Schema

```json
{
  // === ORIGINAL ARTICLE DATA ===
  "uri": "string",                           // Unique article identifier
  "title": "string",                         // Article title
  "body": "string",                          // Full article content
  "source_name": "string",                   // Source publication name
  "published_at": "ISO8601",                 // Publication timestamp
  "url": "string",                           // Original article URL
  "image_url": "string",                     // Featured image URL
  "author": "string",                        // Article author
  "location": "string",                      // Geographic location
  "language": "string",                      // Article language (e.g., "eng")
  
  // === ORIGINAL EVENTREGISTRY DATA ===
  "concepts": [                              // Original EventRegistry concepts
    {
      "uri": "string",
      "label": {"eng": "string"},
      "score": "float"
    }
  ],
  "concept_labels": ["string"],              // Extracted concept labels in English
  "social_score": "float",                   // Social media engagement score
  "sentiment": "float",                      // Sentiment analysis score (-1 to 1)
  "relevance": "float",                      // Article relevance score
  
  // === LLM-BASED CATEGORIZATION (NEW) ===
  "custom_category_name": "string",          // LLM-generated category (backward compatibility)
  "custom_category_id": "string",            // Category ID (lowercase, underscored)
  "category_name": "string",                 // Same as custom_category_name
  "category_id": "string",                   // Same as custom_category_id
  "llm_subcategory": "string",               // LLM-generated subcategory
  "subcategory_name": "string",              // Same as llm_subcategory
  "subcategory_id": "string",                // Subcategory ID (lowercase, underscored)
  
  // === LLM-GENERATED CONTENT ===
  "summary": "string",                       // 60-word LLM-generated summary
  "haiku": "string",                         // 5-7-5 syllable haiku summary
  
  // === TOPIC EXTRACTION ===
  "custom_topic_names": ["string"],          // Top 7 topics from concept labels
  "topic_keywords": [                        // Enhanced topic metadata
    {
      "keyword": "string",
      "trending_score": "float",
      "relevance_score": "float"
    }
  ],
  
  // === BREAKING NEWS ANALYSIS ===
  "trending_score": "float",                 // Trending analysis score (0.0-1.0)
  "trending_level": "string",                // "low", "medium", "high", "very_high"
  "viral_velocity": "float",                 // Viral spread velocity (0.0-1.0)
  "breaking_news": "boolean",                // Is this breaking news?
  "breaking_score": "float",                 // Breaking news confidence (0.0-1.0)
  "story_development_stage": "string",       // "developing", "established", "concluded", null
  "related_story_ids": ["string"],           // Related article URIs
  "alert_level": "string",                   // "none", "low", "medium", "high"
  
  // === READING METRICS ===
  "estimated_read_time": "string",           // Human-readable read time ("2 min")
  "read_time_seconds": "integer",            // Read time in seconds
  "reading_difficulty": "string",            // "very_easy" to "very_difficult"
  "reading_grade_level": "string",           // "5th grade" to "graduate level"
  "suitable_for_general_audience": "boolean", // Accessibility flag
  
  "reading_metrics": {                       // Complete reading analysis
    "reading_times": {
      "slow": {
        "minutes": "float",
        "seconds": "integer",
        "formatted": "string"
      },
      "average": {
        "minutes": "float", 
        "seconds": "integer",
        "formatted": "string"
      },
      "fast": {
        "minutes": "float",
        "seconds": "integer", 
        "formatted": "string"
      },
      "speed": {
        "minutes": "float",
        "seconds": "integer",
        "formatted": "string"
      }
    },
    "word_metrics": {
      "word_count": "integer",
      "sentence_count": "integer",
      "paragraph_count": "integer",
      "avg_words_per_sentence": "float",
      "avg_sentences_per_paragraph": "float"
    },
    "complexity_metrics": {
      "flesch_score": "float",               // 0-100 readability score
      "avg_sentence_length": "float",
      "avg_syllables_per_word": "float",
      "technical_terms_count": "integer",
      "technical_terms_ratio": "float",
      "complexity_level": "string"
    },
    "engagement_metrics": {
      "question_count": "integer",
      "exclamation_count": "integer",
      "quote_count": "integer",
      "title_word_count": "integer",
      "title_engagement_score": "float",
      "interactive_elements": "integer",
      "narrative_elements": "integer"
    },
    "structure_metrics": {
      "has_summary": "boolean",
      "summary_ratio": "float",
      "title_body_ratio": "float",
      "content_density": "float",
      "structure_score": "float"
    },
    "attention_metrics": {
      "attention_required_minutes": "float",
      "attention_level": "string",           // "low", "medium", "high", "very_high"
      "focus_breaks_recommended": "integer",
      "optimal_reading_session": "float"
    },
    "accessibility": {
      "grade_level": "string",
      "accessibility_level": "string",       // "very_high", "high", "good", "moderate", "low", "very_low", "expert_only"
      "flesch_score": "float",
      "suitable_for_general_audience": "boolean"
    },
    "calculated_at": "ISO8601"
  },
  
  // === METADATA ===
  "enriched_at": "ISO8601",                  // When enrichment was performed
  "enrichment_version": "string",            // Enrich service version
  "llm_model_used": "string",                // LLM model for categorization
  "processing_time_ms": "integer"            // Total enrichment time
}
```

## Category Values

### LLM Categories (custom_category_name)
- `politics`
- `business` 
- `technology`
- `science`
- `health`
- `sports`
- `entertainment`
- `lifestyle`
- `world_news`
- `human_interest`
- `general`

### LLM Subcategories (llm_subcategory)
**Politics:** elections, legislation, political_parties, government_agencies, public_policy

**Business:** markets, companies, personal_finance, economics, big_tech, start_ups

**Technology:** artificial_intelligence, software_apps, hardware, social_media, gadgets, cybersecurity

**Science:** environment, space_astronomy, biology_medicine, physics_chemistry, nature_wildlife

**Health:** public_health, wellness, mental_health, medical_research

**Sports:** american_football, basketball, soccer, tennis, hockey, athletics

**Entertainment:** movies, tv_shows, music, celebrity_gossip, awards

**Lifestyle:** fashion, food_cooking, travel, home_garden

**World News:** international_relations, conflicts, global_events

**Human Interest:** community, inspiring_stories, social_issues

**General:** general (fallback)

## Breaking News Alert Levels
- `none` - Not breaking news
- `low` - Minor breaking news
- `medium` - Moderate breaking news  
- `high` - Major breaking news

## Reading Difficulty Levels
- `very_easy` - 5th grade level (Flesch 90+)
- `easy` - 6th grade level (Flesch 80-89)
- `fairly_easy` - 7th grade level (Flesch 70-79)
- `standard` - 8th-9th grade level (Flesch 60-69)
- `fairly_difficult` - 10th-12th grade level (Flesch 50-59)
- `difficult` - College level (Flesch 30-49)
- `very_difficult` - Graduate level (Flesch <30)

## Key Changes from Old System

### ✅ New LLM-Based Features
1. **LLM Categorization** - Replaces rules-based categorization
2. **LLM Summaries** - 60-word summaries and haikus
3. **Breaking News Analysis** - Real-time breaking news detection
4. **Reading Metrics** - Comprehensive readability analysis

### ✅ Backward Compatibility
1. **custom_category_name** - Now contains LLM category (not rules-based)
2. **category_name/category_id** - Maintained for existing frontend
3. **custom_topic_names** - Now from concept labels (not keyword matching)

### ❌ Deprecated Fields
1. **Rules-based categorization** - Completely removed
2. **Keyword-based topics** - Replaced with concept-based topics
3. **Manual subcategory mapping** - Now LLM-generated

## Usage Examples

### Frontend Display
```javascript
// Reading time and difficulty
<span>{article.estimated_read_time}</span>
<span>{article.reading_grade_level}</span>

// Breaking news badge
{article.breaking_news && (
  <Badge level={article.alert_level}>
    Breaking News
  </Badge>
)}

// Category navigation
<CategoryLink 
  category={article.custom_category_name}
  subcategory={article.llm_subcategory}
/>
```

### Analytics Queries
```javascript
// Find breaking news
const breakingNews = articles.filter(a => a.breaking_news);

// Accessibility analysis
const accessibleArticles = articles.filter(a => 
  a.suitable_for_general_audience
);

// Reading time distribution
const readingTimes = articles.map(a => a.read_time_seconds);
```

This schema represents the complete evolution from rules-based to AI-powered article enrichment.
