#!/usr/bin/env python3
"""
Test script for topic output format in enrich service.
Tests the new topic document structure for Firestore.
"""

import sys
import os
import json
from datetime import datetime, timezone

# Add the enrich_service directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the functions we want to test
from http_server import (
    calculate_trending_score,
    determine_trending_level,
    map_eventregistry_type_to_schema,
    get_topic_image_url,
    determine_subcategory,
    prepare_document_for_jsonl
)

def test_trending_calculations():
    """Test trending score and level calculations."""
    print("Testing trending calculations...")
    
    test_cases = [
        (1, 0.0, "cold"),
        (2, 5.0, "trending"),
        (4, 7.2, "trending"),
        (9, 16.2, "hot"),
        (15, 18.0, "hot")
    ]
    
    for article_count, expected_score, expected_level in test_cases:
        score = calculate_trending_score(article_count)
        level = determine_trending_level(score)
        
        print(f"  Articles: {article_count} -> Score: {score} (expected: {expected_score}), Level: {level} (expected: {expected_level})")
        
        # Allow some tolerance for floating point calculations
        assert abs(score - expected_score) < 0.1, f"Score mismatch for {article_count} articles"
        assert level == expected_level, f"Level mismatch for {article_count} articles"
    
    print("✅ Trending calculations tests passed!")

def test_topic_classification():
    """Test topic classification logic using EventRegistry concept types."""
    print("\nTesting topic classification...")

    # Test cases with EventRegistry concept types
    test_cases = [
        ("Fatboy Slim", "person"),
        ("Denzel Curry", "person"),
        ("Alanis Morissette", "person"),
        ("Apple Inc.", "org"),  # EventRegistry uses "org" not "organization"
        ("Microsoft Corporation", "org"),
        ("New York City", "loc"),  # EventRegistry uses "loc" not "location"
        ("artificial intelligence", "wiki"),  # EventRegistry uses "wiki" for general topics
        ("cryptocurrency", "wiki"),
        ("Olympic Games", "event"),
        ("Presidential Election", "event")
    ]

    # Since we're now using EventRegistry concept types directly,
    # we'll test the mapping from EventRegistry types to our schema types

    for topic_name, expected_er_type in test_cases:
        # Test the mapping function
        schema_classification = map_eventregistry_type_to_schema(expected_er_type)
        print(f"  '{topic_name}' -> EventRegistry: {expected_er_type}, Schema: {schema_classification}")

        # The actual classification will come from the concepts field in real data
        # For now, just verify our mapping logic works
        assert schema_classification in ['person', 'organization', 'location', 'topic', 'event']

    print("✅ Topic classification mapping tests passed!")

def test_topic_document_structure():
    """Test the complete topic document structure."""
    print("\nTesting topic document structure...")
    
    # Simulate topic data
    test_topics = [
        {"name": "Fatboy Slim", "count": 4, "category": "world"},
        {"name": "Denzel Curry", "count": 9, "category": "world"},
        {"name": "artificial intelligence", "count": 15, "category": "technology"}
    ]
    
    current_time = datetime.now(timezone.utc)
    current_time_iso = current_time.isoformat()
    
    topic_documents = []
    
    for topic_data in test_topics:
        topic_name = topic_data["name"]
        topic_count = topic_data["count"]
        category_name = topic_data["category"]
        
        # Generate topic document (same logic as in enrich service)
        subcategory = determine_subcategory(category_name, topic_name)
        trending_score = calculate_trending_score(topic_count)
        trending_level = determine_trending_level(trending_score)

        # Simulate EventRegistry concept classification
        if "artificial intelligence" in topic_name.lower():
            classification = map_eventregistry_type_to_schema("wiki")
        elif any(name in topic_name for name in ["Fatboy", "Denzel"]):
            classification = map_eventregistry_type_to_schema("person")
        else:
            classification = map_eventregistry_type_to_schema("topic")

        topic_id = topic_name.lower().replace(' ', '_').replace('-', '_')
        
        topic_doc = {
            'name': topic_name,
            'classification': classification,
            'category_name': category_name,
            'subcategory_name': subcategory,
            'articleCount': topic_count,
            'trendingScore': trending_score,
            'trendingLevel': trending_level,
            'createdAt': current_time_iso,
            'updatedAt': current_time_iso
        }
        
        # Add optional ID field if topic has special characters or is important
        if any(char in topic_name for char in [' ', '-', '&']) or topic_count > 5:
            topic_doc['id'] = topic_id
        
        # Add optional imageUrl if available
        image_url = get_topic_image_url(topic_name, classification)
        if image_url:
            topic_doc['imageUrl'] = image_url
        
        topic_documents.append(topic_doc)
    
    # Test each topic document
    required_fields = ['name', 'classification', 'category_name', 'subcategory_name', 
                      'articleCount', 'trendingScore', 'trendingLevel', 'createdAt', 'updatedAt']
    
    for i, topic_doc in enumerate(topic_documents):
        topic_name = topic_doc['name']
        print(f"\n  Topic {i+1}: {topic_name}")
        
        # Check all required fields are present
        for field in required_fields:
            assert field in topic_doc, f"Missing required field: {field}"
            print(f"    {field}: {topic_doc[field]} ({type(topic_doc[field]).__name__})")
        
        # Validate field types
        assert isinstance(topic_doc['name'], str), "name should be string"
        assert isinstance(topic_doc['classification'], str), "classification should be string"
        assert isinstance(topic_doc['category_name'], str), "category_name should be string"
        assert isinstance(topic_doc['subcategory_name'], str), "subcategory_name should be string"
        assert isinstance(topic_doc['articleCount'], int), "articleCount should be number"
        assert isinstance(topic_doc['trendingScore'], float), "trendingScore should be number"
        assert isinstance(topic_doc['trendingLevel'], str), "trendingLevel should be string"
        assert isinstance(topic_doc['createdAt'], str), "createdAt should be string"
        assert isinstance(topic_doc['updatedAt'], str), "updatedAt should be string"
        
        # Check optional fields
        if 'id' in topic_doc:
            assert isinstance(topic_doc['id'], str), "id should be string"
            print(f"    id: {topic_doc['id']} (str)")
        
        if 'imageUrl' in topic_doc:
            assert isinstance(topic_doc['imageUrl'], str), "imageUrl should be string"
            print(f"    imageUrl: {topic_doc['imageUrl']} (str)")
    
    print("✅ Topic document structure tests passed!")
    return topic_documents

def test_topic_jsonl_serialization():
    """Test that topic documents can be properly serialized to JSONL format."""
    print("\nTesting topic JSONL serialization...")
    
    # Get topic documents from the structure test
    topic_documents = test_topic_document_structure()
    
    # Test JSONL serialization
    jsonl_lines = []
    for topic_doc in topic_documents:
        # Use the same serialization helper as the enrich service
        serializable_doc = prepare_document_for_jsonl(topic_doc)
        jsonl_lines.append(json.dumps(serializable_doc))
    
    jsonl_data = '\n'.join(jsonl_lines)
    print(f"  JSONL data length: {len(jsonl_data)} characters")
    
    # Test that it can be parsed back
    lines = jsonl_data.strip().split('\n')
    for line in lines:
        parsed = json.loads(line)
        assert 'name' in parsed
        assert 'classification' in parsed
        assert 'category_name' in parsed
        assert 'subcategory_name' in parsed
        assert 'articleCount' in parsed
        assert 'trendingScore' in parsed
        assert 'trendingLevel' in parsed
        assert 'createdAt' in parsed
        assert 'updatedAt' in parsed
    
    print("✅ Topic JSONL serialization tests passed!")

def print_sample_topic_documents():
    """Print sample topic documents for reference."""
    print("\n📋 Sample Topic Documents:")
    
    current_time = datetime.now(timezone.utc)
    
    samples = [
        {
            "name": "Fatboy Slim",
            "classification": "person",
            "category_name": "world",
            "subcategory_name": "general",
            "articleCount": 4,
            "trendingScore": 7.2,
            "trendingLevel": "trending",
            "createdAt": current_time.isoformat(),
            "updatedAt": current_time.isoformat(),
            "id": "fatboy_slim"
        },
        {
            "name": "artificial intelligence",
            "classification": "topic",
            "category_name": "technology",
            "subcategory_name": "artificial intelligence",
            "articleCount": 15,
            "trendingScore": 18.0,
            "trendingLevel": "hot",
            "createdAt": current_time.isoformat(),
            "updatedAt": current_time.isoformat(),
            "id": "artificial_intelligence"
        }
    ]
    
    for sample in samples:
        print(json.dumps(sample, indent=2))
        print()

if __name__ == "__main__":
    print("🧪 Testing Topic Document Format\n")
    
    try:
        test_trending_calculations()
        test_topic_classification()
        test_topic_document_structure()
        test_topic_jsonl_serialization()
        print_sample_topic_documents()
        
        print("🎉 All topic format tests passed!")
        print("\n📋 Summary:")
        print("✅ Required Firestore fields present")
        print("✅ Correct field types (string, number)")
        print("✅ Trending score and level calculation")
        print("✅ Topic classification logic")
        print("✅ Subcategory determination")
        print("✅ JSONL serialization compatibility")
        print("✅ Optional fields (id, imageUrl) handling")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
