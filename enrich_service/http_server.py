"""
HTTP API Server for Enrich Service

Provides HTTP endpoints for article enrichment including:
- LLM-based summarization
- Article categorization
- Batch processing
- Health checks and monitoring
"""

import os
import sys
import json
import time
import asyncio
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException, BackgroundTasks, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field

# Import shared utilities
sys.path.append('../shared_utils')
from logging_config import setup_cloud_logging, log_performance_metrics
from health_check import create_enrich_service_health_checker
from metrics import get_metrics

# Import enrich service modules
from summarizer import EnhancedSummarizationService
from models import ArticleEnrichmentRequest, ArticleEnrichmentResponse, BatchEnrichmentRequest, OriginalArticleData
from config_api import config_router
from config import SOURCE_IMAGE_CONFIG

# Global instances
logger = None
metrics = None
summarization_service = None
health_checker = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan management"""
    global logger, metrics, summarization_service, health_checker
    
    # Initialize logging
    logger = setup_cloud_logging("enrich_service")
    logger.info("Starting Enrich Service HTTP API")
    
    # Initialize metrics
    metrics = get_metrics("enrich_service")
    
    # Initialize summarization service
    try:
        summarization_service = EnhancedSummarizationService()
        logger.info("Summarization service initialized")
    except Exception as e:
        logger.error("Failed to initialize summarization service", error=e)
        raise
    
    # Initialize health checker
    health_checker = create_enrich_service_health_checker()
    
    logger.info("Enrich Service HTTP API started successfully")
    yield
    
    # Cleanup
    logger.info("Enrich Service HTTP API stopped")

# Create FastAPI app
app = FastAPI(
    title="News Article Enrichment Service",
    description="LLM-powered article enrichment with summarization and categorization",
    version="1.0.0",
    lifespan=lifespan
)

# Include configuration router
app.include_router(config_router)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Request/Response models
class EnrichArticleRequest(BaseModel):
    """Request model for single article enrichment"""
    article: Dict[str, Any] = Field(..., description="Article data to enrich")
    include_summary: bool = Field(default=True, description="Whether to generate summary")
    include_categorization: bool = Field(default=True, description="Whether to categorize article")

class EnrichArticleResponse(BaseModel):
    """Response model for single article enrichment"""
    success: bool
    article: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    processing_time_seconds: float
    tokens_used: Optional[int] = None
    cost_usd: Optional[float] = None

class BatchEnrichRequest(BaseModel):
    """Request model for batch enrichment"""
    articles: List[Dict[str, Any]] = Field(..., description="List of articles to enrich")
    include_summary: bool = Field(default=True, description="Whether to generate summaries")
    include_categorization: bool = Field(default=True, description="Whether to categorize articles")
    max_concurrent: int = Field(default=5, ge=1, le=20, description="Maximum concurrent requests")

class BatchEnrichResponse(BaseModel):
    """Response model for batch enrichment"""
    success: bool
    results: List[EnrichArticleResponse]
    total_articles: int
    successful_articles: int
    failed_articles: int
    total_processing_time_seconds: float
    total_tokens_used: int
    total_cost_usd: float

# Dependency to get services
def get_summarization_service() -> EnhancedSummarizationService:
    if summarization_service is None:
        raise HTTPException(status_code=503, detail="Summarization service not initialized")
    return summarization_service

# Helper functions for pipeline processing
async def enrich_single_article_pipeline(article: Dict[str, Any], service: EnhancedSummarizationService) -> Dict[str, Any]:
    """Enrich a single article with categorization, breaking news analysis, and summarization"""
    try:
        # LLM-based categorization (replacing old rules-based system)
        try:
            # Convert article to the format expected by summarizer
            from models import OriginalArticleData

            article_data = OriginalArticleData(
                uri=article.get('uri', ''),
                title=article.get('title', ''),
                body=article.get('body', ''),
                source_title=article.get('source_name', ''),
                original_categories=[],  # Not used for LLM categorization
                concept_labels=article.get('concept_labels', [])
            )

            # Get LLM categorization from summarizer
            llm_result = await service.process_single_article(article_data)

            if llm_result and not llm_result.error:
                # Use LLM categorization
                article['custom_category_name'] = llm_result.llm_category
                article['custom_subcategory_name'] = llm_result.llm_subcategory

                # Preserve existing topic_keywords if they exist (from transform service)
                # Only create simplified version if topic_keywords is missing or empty
                if not article.get('topic_keywords'):
                    article['topic_keywords'] = [
                        {
                            'keyword': topic,
                            'trending_score': 0.5,
                            'relevance_score': 0.8
                        } for topic in article.get('concept_labels', [])[:7]
                    ]

                # Extract custom_topic_names from topic_keywords field
                topic_keywords = article.get('topic_keywords', [])
                article['custom_topic_names'] = [
                    kw.get('keyword', '') for kw in topic_keywords
                    if isinstance(kw, dict) and kw.get('keyword')
                ][:7]  # Limit to top 7 topics

                # Add LLM summary if available
                if llm_result.summary:
                    article['summary'] = llm_result.summary

                # Add haiku if available
                if llm_result.haiku:
                    article['haiku'] = llm_result.haiku

                # Add key takeaways if available (convert to ordered format for Firestore)
                if llm_result.key_takeaways:
                    # Sort by index to ensure proper ordering and convert to simple list
                    sorted_takeaways = sorted(llm_result.key_takeaways, key=lambda x: x.index)
                    article['key_takeaways'] = [takeaway.text for takeaway in sorted_takeaways]

            else:
                # Fallback to default categorization if LLM fails
                logger.warning(f"LLM categorization failed for {article.get('uri', 'unknown')}: {llm_result.error if llm_result else 'No result'}")
                article['custom_category_name'] = 'general'
                article['custom_subcategory_name'] = 'general'
                article['custom_topic_names'] = article.get('concept_labels', [])[:7]
                article['topic_keywords'] = []

        except Exception as e:
            logger.error(f"Error in LLM categorization for {article.get('uri', 'unknown')}: {e}")
            # Fallback to default categorization
            article['custom_category_name'] = 'general'
            article['custom_subcategory_name'] = 'general'
            article['custom_topic_names'] = article.get('concept_labels', [])[:7]
            article['topic_keywords'] = []

        # Add default breaking news fields (breaking news analysis is handled in transform service)
        article['trending_score'] = 0.0
        article['trending_level'] = 'low'
        article['viral_velocity'] = 0.0
        article['breaking_news'] = False
        article['breaking_score'] = 0.0
        article['story_development_stage'] = None
        article['related_story_ids'] = []
        article['alert_level'] = 'none'

        # Generate summary if body exists
        if article.get('body'):
            # Convert article to the format expected by summarizer
            from models import OriginalArticleData

            article_data = OriginalArticleData(
                uri=article.get('uri', ''),
                title=article.get('title', ''),
                body=article.get('body', ''),
                source_title=article.get('source_name', ''),
                original_categories=[],  # Not used for LLM categorization
                concept_labels=article.get('concept_labels', [])
            )

            summary_result = await service.process_single_article(article_data)
            if summary_result and hasattr(summary_result, 'summary'):
                article['summary'] = summary_result.summary

        # Calculate reading metrics
        try:
            from reading_metrics import reading_metrics_calculator
            reading_metrics = reading_metrics_calculator.calculate_all_metrics(article)
            article['reading_metrics'] = reading_metrics

            # Add convenient top-level fields for easy access
            if 'reading_times' in reading_metrics:
                article['estimated_read_time'] = reading_metrics['reading_times']['average']['formatted']
                article['read_time_seconds'] = reading_metrics['reading_times']['average']['seconds']

            if 'difficulty_level' in reading_metrics:
                article['reading_difficulty'] = reading_metrics['difficulty_level']

            if 'accessibility' in reading_metrics:
                article['reading_grade_level'] = reading_metrics['accessibility']['grade_level']
                article['suitable_for_general_audience'] = reading_metrics['accessibility']['suitable_for_general_audience']

        except Exception as e:
            logger.error(f"Error calculating reading metrics for article {article.get('uri', 'unknown')}: {e}")
            # Add default reading metrics
            article['reading_metrics'] = {'error': 'Failed to calculate reading metrics'}
            article['estimated_read_time'] = 'Unknown'
            article['read_time_seconds'] = 0
            article['reading_difficulty'] = 'unknown'

        # Add enrichment metadata and timestamps
        from datetime import datetime, timezone
        current_time = datetime.now(timezone.utc).isoformat()

        article['enriched_at'] = current_time
        article['enrichment_version'] = '2.0'
        article['llm_model_used'] = 'gpt-4.1-nano'

        # Ensure timestamps are present (set defaults if missing)
        if not article.get('created_at'):
            article['created_at'] = current_time
        if not article.get('last_updated'):
            article['last_updated'] = current_time

        return article

    except Exception as e:
        logger.error(f"Error enriching article {article.get('uri', 'unknown')}: {e}")
        return article

def calculate_trending_score(article_count: int) -> float:
    """Calculate trending score based on article count and other factors."""
    # Simple trending score calculation - can be enhanced with time-based factors
    if article_count <= 1:
        return 0.0
    elif article_count <= 3:
        return round(article_count * 2.5, 3)
    elif article_count <= 10:
        return round(article_count * 1.8, 3)
    else:
        return round(article_count * 1.2, 3)

def determine_trending_level(trending_score: float) -> str:
    """Determine trending level based on trending score."""
    if trending_score == 0:
        return "cold"
    elif trending_score < 5:
        return "cold"
    elif trending_score < 15:
        return "trending"
    else:
        return "hot"

def determine_topic_classification(topic_name: str) -> str:
    """Determine the classification type of a topic (person, organization, location, etc.)."""
    topic_lower = topic_name.lower()

    # Event indicators (check first to avoid misclassification)
    event_indicators = [
        'conference', 'summit', 'meeting', 'festival', 'championship', 'olympics',
        'election', 'vote', 'campaign', 'protest', 'rally', 'ceremony', 'games',
        'cup', 'tournament', 'awards', 'show', 'concert', 'exhibition'
    ]

    # Organization indicators
    org_indicators = [
        'inc.', 'corp.', 'ltd.', 'llc', 'company', 'corporation', 'group',
        'bank', 'university', 'college', 'hospital', 'foundation', 'institute',
        'association', 'organization', 'agency', 'department', 'ministry'
    ]

    # Location indicators
    location_indicators = [
        'city', 'county', 'state', 'country', 'province', 'region', 'district',
        'street', 'avenue', 'road', 'boulevard', 'square', 'park'
    ]

    # Common person name patterns
    person_indicators = [
        # Common titles
        'mr.', 'mrs.', 'ms.', 'dr.', 'prof.', 'president', 'ceo', 'minister',
        # Common name patterns (simple heuristics)
        'john', 'jane', 'michael', 'sarah', 'david', 'mary', 'robert', 'jennifer'
    ]

    # Check for event first (to avoid misclassifying "Olympic Games" as person)
    if any(indicator in topic_lower for indicator in event_indicators):
        return "event"

    # Check for organization
    if any(indicator in topic_lower for indicator in org_indicators):
        return "organization"

    # Check for location
    if any(indicator in topic_lower for indicator in location_indicators):
        return "location"

    # Check for person (if it looks like a name with 2-3 words and proper capitalization)
    words = topic_name.split()
    if len(words) >= 2 and len(words) <= 3:
        # Check if all words are capitalized (name pattern)
        if all(word[0].isupper() for word in words if word):
            # Additional check for person indicators
            if any(indicator in topic_lower for indicator in person_indicators):
                return "person"
            # If it's 2-3 capitalized words without org/location/event indicators, likely a person
            if not any(indicator in topic_lower for indicator in org_indicators + location_indicators + event_indicators):
                return "person"

    # Default classification
    return "topic"

def map_eventregistry_type_to_schema(er_type: str) -> str:
    """Map EventRegistry concept type to our schema classification."""
    mapping = {
        'person': 'person',
        'org': 'organization',
        'loc': 'location',
        'wiki': 'topic',
        'event': 'event',
        'concept': 'topic'  # fallback
    }
    return mapping.get(er_type, 'topic')

def get_topic_image_url(topic_name: str, classification: str) -> str:
    """Get image URL for a topic if available (placeholder implementation)."""
    # For now, return None - this could be enhanced to fetch from Wikipedia API
    # or other image sources based on topic name and classification
    return None

def determine_subcategory(category_name: str, topic_name: str) -> str:
    """Determine subcategory based on category and topic using dynamic configuration"""
    from category_config import category_config_manager

    try:
        return category_config_manager.get_subcategory_for_topic(category_name, topic_name)
    except Exception as e:
        logger.error(f"Error determining subcategory: {e}")
        return "general"

def determine_subcategory_legacy(category_name: str, topic_name: str) -> str:
    """Legacy subcategory determination (fallback)"""
    topic_lower = topic_name.lower()

    # Subcategory mapping based on topic keywords
    subcategory_mappings = {
        "Politics": {
            "elections": ["election", "vote", "campaign", "candidate", "ballot"],
            "legislation": ["law", "bill", "congress", "senate", "parliament", "policy"],
            "political parties": ["democrat", "republican", "party", "political"],
            "government agencies": ["government", "agency", "department", "federal"],
            "public policy": ["policy", "regulation", "reform", "initiative"]
        },
        "Business": {
            "markets": ["market", "stock", "trading", "exchange", "dow", "nasdaq"],
            "companies": ["company", "corporation", "business", "firm", "enterprise"],
            "personal finance": ["finance", "money", "investment", "savings", "retirement"],
            "economics": ["economy", "economic", "gdp", "inflation", "recession"],
            "big tech": ["apple", "google", "microsoft", "amazon", "meta", "tesla"],
            "start ups": ["startup", "venture", "funding", "entrepreneur"]
        },
        "Technology": {
            "artificial intelligence": ["ai", "artificial intelligence", "machine learning", "neural"],
            "software & apps": ["software", "app", "application", "program", "code"],
            "hardware": ["hardware", "chip", "processor", "device", "computer"],
            "social media": ["social", "facebook", "twitter", "instagram", "tiktok"],
            "gadgets": ["gadget", "phone", "smartphone", "tablet", "laptop"],
            "cybersecurity": ["cyber", "security", "hack", "breach", "privacy"]
        },
        "Science": {
            "environment": ["environment", "climate", "pollution", "carbon", "green"],
            "space & astronomy": ["space", "nasa", "astronomy", "planet", "satellite"],
            "biology & medicine": ["biology", "medical", "health", "disease", "research"],
            "physics & chemistry": ["physics", "chemistry", "quantum", "molecule"],
            "nature & wildlife": ["nature", "wildlife", "animal", "species", "conservation"]
        },
        "Sports": {
            "american football": ["football", "nfl", "quarterback", "touchdown"],
            "basketball": ["basketball", "nba", "court", "dunk", "playoff"],
            "soccer": ["soccer", "football", "fifa", "world cup", "goal"],
            "tennis": ["tennis", "wimbledon", "court", "serve", "match"],
            "hockey": ["hockey", "nhl", "ice", "puck", "goal"],
            "athletics": ["track", "field", "marathon", "olympics", "athlete"]
        },
        "Entertainment": {
            "movies": ["movie", "film", "cinema", "actor", "director", "oscar"],
            "tv shows": ["tv", "television", "series", "show", "episode"],
            "music": ["music", "song", "album", "artist", "concert", "grammy"],
            "celebrity gossip": ["celebrity", "gossip", "scandal", "relationship"],
            "awards": ["award", "oscar", "grammy", "emmy", "golden globe"]
        },
        "Health": {
            "public health": ["public health", "epidemic", "vaccine", "healthcare"],
            "wellness": ["wellness", "fitness", "nutrition", "diet", "exercise"],
            "mental health": ["mental", "depression", "anxiety", "therapy"],
            "medical research": ["research", "study", "clinical", "trial", "medicine"]
        },
        "Lifestyle": {
            "fashion": ["fashion", "style", "clothing", "designer", "trend"],
            "food & cooking": ["food", "cooking", "recipe", "restaurant", "chef"],
            "travel": ["travel", "vacation", "tourism", "destination", "hotel"],
            "home & garden": ["home", "garden", "interior", "design", "diy"]
        },
        "World News": {
            "international relations": ["diplomatic", "embassy", "foreign", "international"],
            "conflicts": ["war", "conflict", "military", "peace", "treaty"],
            "global events": ["global", "world", "international", "summit", "conference"]
        },
        "Human Interest": {
            "community": ["community", "local", "neighborhood", "volunteer"],
            "inspiring stories": ["inspiring", "hero", "rescue", "heartwarming"],
            "social issues": ["social", "justice", "rights", "equality", "activism"]
        }
    }

    # Find matching subcategory
    if category_name in subcategory_mappings:
        for subcategory, keywords in subcategory_mappings[category_name].items():
            for keyword in keywords:
                if keyword in topic_lower:
                    return subcategory

    # Default subcategory
    return "general"

async def trigger_load_service(job_date: str, job_timestamp: str, gcs_bucket: str, enriched_path: str, article_count: int) -> Dict[str, Any]:
    """Trigger load service after enrichment"""
    import os
    import requests

    load_service_url = os.getenv("LOAD_SERVICE_URL", "http://localhost:8082")

    payload = {
        'job_date_str': job_date,
        'job_run_timestamp_str': job_timestamp,
        'gcs_bucket_name': gcs_bucket,
        'enriched_data_path': enriched_path,
        'total_articles_enriched': article_count
    }

    try:
        response = requests.post(
            f"{load_service_url}/load/enriched",
            json=payload,
            timeout=300
        )

        if response.status_code == 200:
            return {'status': 'success', 'response': response.json()}
        else:
            return {'status': 'error', 'error': f"HTTP {response.status_code}"}

    except Exception as e:
        return {'status': 'error', 'error': str(e)}

async def trigger_embed_service(job_date: str, job_timestamp: str, gcs_bucket: str, enriched_path: str, article_count: int) -> Dict[str, Any]:
    """Trigger embed service in parallel with load service (original architecture)"""
    import os
    import requests

    embed_service_url = os.getenv("EMBED_SERVICE_URL", "https://embed-service-429922701640.us-central1.run.app")

    payload = {
        'gcs_location': f"gs://{gcs_bucket}/{enriched_path}/",
        'batch_id': f"batch_{job_date}_{job_timestamp}",
        'article_count': article_count
    }

    try:
        response = requests.post(
            f"{embed_service_url}/embed",
            json=payload,
            timeout=300
        )

        if response.status_code == 200:
            return {'status': 'success', 'response': response.json()}
        else:
            return {'status': 'error', 'error': f"HTTP {response.status_code}"}

    except Exception as e:
        return {'status': 'error', 'error': str(e)}

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    if health_checker is None:
        return {"status": "unhealthy", "error": "Health checker not initialized"}
    
    result = health_checker.perform_health_check()
    status_code = 200 if result["status"] == "healthy" else 503
    
    return result

# Single article enrichment
@app.post("/enrich", response_model=EnrichArticleResponse)
async def enrich_article(
    request: EnrichArticleRequest,
    service: EnhancedSummarizationService = Depends(get_summarization_service)
):
    """Enrich a single article with summarization and categorization"""
    start_time = time.time()
    
    try:
        article = request.article.copy()
        tokens_used = 0
        cost_usd = 0.0
        
        # Process with LLM if categorization or summary requested
        if request.include_categorization or request.include_summary:
            with metrics.timer("llm_processing"):
                # Convert to OriginalArticleData for the summarizer
                from models import OriginalArticleData
                article_data = OriginalArticleData(
                    uri=article.get('uri', 'unknown'),
                    title=article.get('title', ''),
                    body=article.get('body', ''),
                    source_title=article.get('source_name', ''),
                    concept_labels=article.get('concept_labels', [])
                )

                # Process with LLM
                llm_result = await summarization_service.process_single_article(article_data)

                if not llm_result.error:
                    # Update article with LLM results
                    if request.include_categorization:
                        article['custom_category_name'] = llm_result.llm_category
                        article['custom_subcategory_name'] = llm_result.llm_subcategory

                    if request.include_summary:
                        article['summary'] = llm_result.summary
                        # Also include haiku and key_takeaways when summary is requested
                        if llm_result.haiku:
                            article['haiku'] = llm_result.haiku
                        if llm_result.key_takeaways:
                            # Sort by index to ensure proper ordering and convert to simple list
                            sorted_takeaways = sorted(llm_result.key_takeaways, key=lambda x: x.index)
                            article['key_takeaways'] = [takeaway.text for takeaway in sorted_takeaways]

                    cost_usd = llm_result.processing_time or 0.0
                else:
                    logger.error(f"LLM processing failed: {llm_result.error}")
                    article['llm_error'] = llm_result.error

        
        processing_time = time.time() - start_time
        
        # Record metrics
        metrics.record_articles_processed(1, "success")
        if tokens_used > 0:
            metrics.record_llm_usage("gpt-4.1-nano", tokens_used, 0, cost_usd)
        
        log_performance_metrics(
            logger,
            "article_enrichment",
            processing_time,
            True,
            tokens_used=tokens_used,
            cost_usd=cost_usd
        )
        
        return EnrichArticleResponse(
            success=True,
            article=article,
            processing_time_seconds=processing_time,
            tokens_used=tokens_used,
            cost_usd=cost_usd
        )
        
    except Exception as e:
        processing_time = time.time() - start_time
        
        metrics.record_error("enrichment_failed", "enrich_article")
        logger.error("Article enrichment failed", error=e)
        
        return EnrichArticleResponse(
            success=False,
            error=str(e),
            processing_time_seconds=processing_time
        )

def serialize_datetime_for_json(obj):
    """Convert datetime objects to ISO strings for JSON serialization."""
    if isinstance(obj, datetime):
        return obj.isoformat()
    return obj

def prepare_document_for_jsonl(document: Dict[str, Any]) -> Dict[str, Any]:
    """Prepare a document for JSONL serialization by converting datetime objects."""
    serializable_doc = {}
    for key, value in document.items():
        serializable_doc[key] = serialize_datetime_for_json(value)
    return serializable_doc

def extract_initials(source_title: str) -> str:
    """Extract initials from source title for fallback image generation."""
    if not source_title:
        return "NS"  # News Source

    # Remove common words and clean (matching transform service logic)
    words = source_title.replace("The ", "").replace(".", "").split()

    # Extract first letter of each significant word (skip short words)
    initials = "".join([word[0].upper() for word in words if len(word) > 2])

    # Limit to 2-3 characters
    return initials[:3] if len(initials) <= 3 else initials[:2]

def generate_source_image_url(source_uri: str) -> str:
    """Generate source image URL using logo.dev with token."""
    if not source_uri:
        return ""

    # Extract domain from URI
    domain = source_uri
    if source_uri.startswith(('http://', 'https://')):
        from urllib.parse import urlparse
        parsed = urlparse(source_uri)
        domain = parsed.netloc

    # Remove www. prefix if present
    if domain.startswith('www.'):
        domain = domain[4:]

    token = SOURCE_IMAGE_CONFIG["logo_dev_token"]
    size = SOURCE_IMAGE_CONFIG["image_size"]
    format_type = SOURCE_IMAGE_CONFIG["image_format"]

    return f"https://img.logo.dev/{domain}?token={token}&size={size}&format={format_type}"

def extract_sources_from_articles(articles: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Extract and deduplicate sources from enriched articles."""
    if logger:
        logger.info("Extracting sources from enriched articles...")
    else:
        print("Extracting sources from enriched articles...")

    unique_sources_dict = {}
    current_time = datetime.now(timezone.utc).isoformat()

    for article in articles:
        try:
            # Get source information from article
            source_name = article.get('source_name')
            source_uri = article.get('source_uri', '')
            source_url = article.get('url', '')

            if not source_name:
                continue

            # Create a unique identifier for the source
            if source_uri:
                uri = source_uri
            elif source_url:
                # Extract domain from URL as URI
                try:
                    from urllib.parse import urlparse
                    parsed_url = urlparse(source_url)
                    uri = parsed_url.netloc.lower()
                except:
                    uri = source_name.lower().replace(' ', '_')
            else:
                uri = source_name.lower().replace(' ', '_')

            # Skip if we already have this source
            if uri in unique_sources_dict:
                continue

            # Create source object with required Firestore fields
            source_data = {
                # Required Firestore fields
                'created_at': current_time,
                'description': f"News source: {source_name}",
                'lastUpdated': current_time,
                'last_article_date': None,  # Will be updated by load service
                'last_updated': current_time,
                'load_timestamp': current_time,
                'source_image_url': generate_source_image_url(source_uri or source_url),
                'source_name': source_name,
                'source_url': source_uri or source_url,

                # Additional fields for compatibility
                'uri': uri,
                'dataType': 'news',

                # Location information if available
                'source_location_place': article.get('source_location_place', ''),
                'source_location_country': article.get('source_location_country', ''),
            }

            # Add location object if we have coordinate data
            if article.get('article_location'):
                source_data['location'] = article.get('article_location')

            unique_sources_dict[uri] = source_data

        except Exception as e:
            if logger:
                logger.warning(f"Error extracting source from article '{article.get('title', 'Unknown')}': {e}")
            else:
                print(f"Warning: Error extracting source from article '{article.get('title', 'Unknown')}': {e}")

    sources_list = list(unique_sources_dict.values())
    if logger:
        logger.info(f"Extracted {len(sources_list)} unique sources from {len(articles)} articles")
    else:
        print(f"Extracted {len(sources_list)} unique sources from {len(articles)} articles")

    return sources_list

# Pipeline batch processing (from transform service)
@app.post("/enrich/batch")
async def enrich_pipeline_batch(
    request: dict,
    background_tasks: BackgroundTasks,
    service: EnhancedSummarizationService = Depends(get_summarization_service)
):
    """Process articles from transform service and generate enriched output for load service"""
    start_time = time.time()

    try:
        # Extract request parameters
        input_path = request.get('input_path')
        output_prefix = request.get('output_prefix')
        job_date = request.get('job_date')
        job_timestamp = request.get('job_timestamp')
        article_count = request.get('article_count', 0)
        gcs_bucket = request.get('gcs_bucket')

        if not all([input_path, output_prefix, gcs_bucket]):
            raise HTTPException(status_code=400, detail="Missing required parameters")

        logger.info("Pipeline batch enrichment started",
                   input_path=input_path,
                   output_prefix=output_prefix,
                   article_count=article_count)

        # Download articles from GCS using REST client
        import sys
        sys.path.append('../shared_utils')
        from gcp_rest_client import StorageRestClient

        gcs_client = StorageRestClient()

        # Download transformed articles
        blob_path = f"{input_path.rstrip('/')}/transformed_articles.jsonl"
        articles_content = gcs_client.download_text(gcs_bucket, blob_path)

        if not articles_content:
            raise HTTPException(status_code=404, detail=f"No transformed articles found at gs://{gcs_bucket}/{blob_path}")

        # Parse articles
        articles = []
        for line in articles_content.strip().split('\n'):
            if line.strip():
                articles.append(json.loads(line))

        logger.info(f"Downloaded {len(articles)} articles for enrichment")

        # Process articles with enrichment
        enriched_articles = []
        categories_data = {}
        topics_data = {}  # Store topic metadata including classification from concepts
        subcategories_data = {}  # Store subcategory metadata for JSONL output

        for article in articles:
            # Enrich article (categorization + breaking news analysis + summarization)
            enriched_article = await enrich_single_article_pipeline(article, service)
            enriched_articles.append(enriched_article)

            # Collect category and topic data
            category_name = enriched_article.get('custom_category_name')
            if category_name:
                if category_name not in categories_data:
                    categories_data[category_name] = {
                        'category_id': enriched_article.get('custom_category_id'),
                        'category_name': category_name,
                        'article_count': 0,
                        'topics': {}
                    }
                categories_data[category_name]['article_count'] += 1

                # Collect topics and concept metadata
                concepts = enriched_article.get('concepts', [])
                concept_map = {}
                for concept in concepts:
                    label = concept.get('label', {}).get('eng', '')
                    if label:
                        er_type = concept.get('type', 'topic')
                        schema_type = map_eventregistry_type_to_schema(er_type)
                        concept_map[label] = {
                            'type': schema_type,
                            'image_url': concept.get('image', ''),
                            'trending_score': concept.get('trendingScore', {}).get('news', {}).get('score', 0)
                        }

                for topic in enriched_article.get('custom_topic_names', []):
                    if topic not in categories_data[category_name]['topics']:
                        categories_data[category_name]['topics'][topic] = 0
                    categories_data[category_name]['topics'][topic] += 1

                    # Store topic metadata for later use
                    if topic not in topics_data:
                        topics_data[topic] = {
                            'classification': concept_map.get(topic, {}).get('type', 'topic'),
                            'image_url': concept_map.get(topic, {}).get('image_url', ''),
                            'trending_score': concept_map.get(topic, {}).get('trending_score', 0),
                            'total_articles': 0
                        }
                    topics_data[topic]['total_articles'] += 1

                    # Track subcategories for this topic
                    subcategory = determine_subcategory(category_name, topic)
                    subcategory_key = f"{category_name}_{subcategory}"

                    if subcategory_key not in subcategories_data:
                        subcategories_data[subcategory_key] = {
                            'name': subcategory,
                            'category_name': category_name,
                            'article_count': 0,
                            'topic_count': 0,
                            'topics': set()
                        }

                    subcategories_data[subcategory_key]['article_count'] += 1
                    subcategories_data[subcategory_key]['topics'].add(topic)

        # Generate category, topic, and subcategory documents
        category_documents = []
        topic_documents = []
        subcategory_documents = []

        current_time = datetime.now(timezone.utc)
        current_time_iso = current_time.isoformat()

        for category_name, cat_data in categories_data.items():
            # Category document with required Firestore fields
            category_documents.append({
                # Required Firestore fields (matching your schema)
                'createdAt': current_time_iso,
                'name': category_name,
                'last_updated': current_time,  # Firestore timestamp
                'load_timestamp': current_time,  # Firestore timestamp
                'topicCount': len(cat_data['topics']),  # Number of unique topics in this category
                'updatedAt': current_time_iso,

                # Additional fields for backward compatibility
                'category_id': cat_data['category_id'],
                'category_name': category_name,
                'article_count': cat_data['article_count']
            })

            # Group topics by subcategory for hierarchical structure
            subcategories_data = {}

            for topic_name, topic_count in cat_data['topics'].items():
                # Determine subcategory for this topic
                subcategory = determine_subcategory(category_name, topic_name)

                # Calculate trending metrics
                trending_score = calculate_trending_score(topic_count)
                trending_level = determine_trending_level(trending_score)

                # Get topic classification from EventRegistry metadata
                topic_metadata = topics_data.get(topic_name, {})
                classification = topic_metadata.get('classification', 'topic')

                # Topic document with required Firestore fields
                topic_doc = {
                    # Required Firestore fields (matching your schema)
                    'name': topic_name,
                    'classification': classification,
                    'category_name': category_name,
                    'subcategory_name': subcategory,
                    'articleCount': topic_count,
                    'trendingScore': trending_score,
                    'trendingLevel': trending_level,
                    'createdAt': current_time_iso,
                    'updatedAt': current_time_iso
                }

                # Note: Removed optional ID field to avoid confusion

                # Add optional imageUrl from EventRegistry if available
                image_url = topic_metadata.get('image_url', '')
                if image_url:
                    topic_doc['imageUrl'] = image_url

                topic_documents.append(topic_doc)

                if subcategory not in subcategories_data:
                    subcategories_data[subcategory] = {
                        'subcategory_id': f"{cat_data['category_id']}_{subcategory.lower().replace(' ', '_')}",
                        'subcategory_name': subcategory,
                        'article_count': 0,
                        'topics': []
                    }

                subcategories_data[subcategory]['article_count'] += topic_count
                subcategories_data[subcategory]['topics'].append({
                    'topic_id': topic_name.lower().replace(' ', '_'),
                    'topic_name': topic_name,
                    'article_count': topic_count
                })



        # Generate subcategory documents
        for subcategory_key, subcat_data in subcategories_data.items():
            # Update topic count
            subcat_data['topic_count'] = len(subcat_data['topics'])

            # Convert topics set to list for JSON serialization
            topics_list = list(subcat_data['topics'])

            subcategory_doc = {
                'name': subcat_data['name'],
                'category_name': subcat_data['category_name'],
                'articleCount': subcat_data['article_count'],
                'topicCount': subcat_data['topic_count'],
                'topics': topics_list,
                'createdAt': current_time_iso,
                'updatedAt': current_time_iso
            }

            subcategory_documents.append(subcategory_doc)

        # Extract sources from enriched articles
        enriched_sources = extract_sources_from_articles(enriched_articles)
        logger.info(f"Generated {len(category_documents)} categories, {len(subcategory_documents)} subcategories, and {len(topic_documents)} topics")
        logger.info(f"Extracted {len(enriched_sources)} unique sources")

        # Upload enriched results to GCS
        clean_prefix = output_prefix.rstrip('/')

        # Upload enriched articles using REST client
        enriched_articles_path = f"{clean_prefix}/enriched_articles.jsonl"
        enriched_jsonl = '\n'.join(json.dumps(article) for article in enriched_articles)
        gcs_client.upload_text(gcs_bucket, enriched_articles_path, enriched_jsonl)

        # Upload categories (backward compatibility)
        categories_path = f"{clean_prefix}/categories.jsonl"
        categories_jsonl = '\n'.join(json.dumps(prepare_document_for_jsonl(cat)) for cat in category_documents)
        gcs_client.upload_text(gcs_bucket, categories_path, categories_jsonl)

        # Upload topics (backward compatibility)
        topics_path = f"{clean_prefix}/topics.jsonl"
        topics_jsonl = '\n'.join(json.dumps(prepare_document_for_jsonl(topic)) for topic in topic_documents)
        gcs_client.upload_text(gcs_bucket, topics_path, topics_jsonl)

        # Upload subcategories (NEW)
        subcategories_path = f"{clean_prefix}/subcategories.jsonl"
        subcategories_jsonl = '\n'.join(json.dumps(prepare_document_for_jsonl(subcat)) for subcat in subcategory_documents)
        gcs_client.upload_text(gcs_bucket, subcategories_path, subcategories_jsonl)

        # Upload sources (NEW - replaces transform service functionality)
        sources_path = f"{clean_prefix}/transformed_sources.jsonl"
        sources_jsonl = '\n'.join(json.dumps(source) for source in enriched_sources)
        gcs_client.upload_text(gcs_bucket, sources_path, sources_jsonl)

        processing_time = time.time() - start_time

        # Trigger both load and embed services in parallel (original architecture)
        import asyncio

        # Create tasks for parallel execution
        load_task = asyncio.create_task(trigger_load_service(
            job_date, job_timestamp, gcs_bucket, clean_prefix, len(enriched_articles)
        ))

        embed_task = asyncio.create_task(trigger_embed_service(
            job_date, job_timestamp, gcs_bucket, clean_prefix, len(enriched_articles)
        ))

        # Wait for both to complete
        load_result, embed_result = await asyncio.gather(load_task, embed_task, return_exceptions=True)

        logger.info("Pipeline batch enrichment completed",
                   articles_processed=len(enriched_articles),
                   categories_generated=len(category_documents),
                   topics_generated=len(topic_documents),
                   sources_generated=len(enriched_sources),
                   processing_time=processing_time)

        return {
            'status': 'success',
            'message': f'Enriched {len(enriched_articles)} articles and {len(enriched_sources)} sources',
            'articles_processed': len(enriched_articles),
            'categories_generated': len(category_documents),
            'subcategories_generated': len(subcategory_documents),
            'topics_generated': len(topic_documents),
            'sources_generated': len(enriched_sources),
            'output_paths': {
                'articles': enriched_articles_path,
                'categories': categories_path,
                'subcategories': subcategories_path,
                'topics': topics_path,
                'sources': sources_path
            },
            'processing_time_seconds': processing_time,
            'load_service_result': load_result,
            'embed_service_result': embed_result,
            'pipeline_architecture': 'Extract → Transform → Enrich → [Load + Embed] → Recommendation'
        }

    except Exception as e:
        processing_time = time.time() - start_time
        logger.error("Pipeline batch enrichment failed", error=e)
        raise HTTPException(status_code=500, detail=str(e))

# Batch enrichment
@app.post("/enrich/batch", response_model=BatchEnrichResponse)
async def enrich_articles_batch(
    request: BatchEnrichRequest,
    background_tasks: BackgroundTasks,
    service: EnhancedSummarizationService = Depends(get_summarization_service)
):
    """Enrich multiple articles in batch"""
    start_time = time.time()
    
    try:
        results = []
        total_tokens = 0
        total_cost = 0.0
        successful_count = 0
        
        # Process articles with concurrency limit
        semaphore = asyncio.Semaphore(request.max_concurrent)
        
        async def process_single_article(article_data):
            async with semaphore:
                enrich_request = EnrichArticleRequest(
                    article=article_data,
                    include_summary=request.include_summary,
                    include_categorization=request.include_categorization
                )
                return await enrich_article(enrich_request, service)
        
        # Create tasks for all articles
        tasks = [process_single_article(article) for article in request.articles]
        
        # Execute all tasks
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        processed_results = []
        for result in results:
            if isinstance(result, Exception):
                processed_results.append(EnrichArticleResponse(
                    success=False,
                    error=str(result),
                    processing_time_seconds=0
                ))
            else:
                processed_results.append(result)
                if result.success:
                    successful_count += 1
                    if result.tokens_used:
                        total_tokens += result.tokens_used
                    if result.cost_usd:
                        total_cost += result.cost_usd
        
        total_processing_time = time.time() - start_time
        failed_count = len(request.articles) - successful_count
        
        # Record batch metrics
        metrics.record_articles_processed(successful_count, "success")
        if failed_count > 0:
            metrics.record_articles_processed(failed_count, "error")
        
        if total_tokens > 0:
            metrics.record_llm_usage("gpt-4.1-nano", total_tokens, 0, total_cost)
        
        log_performance_metrics(
            logger,
            "batch_enrichment",
            total_processing_time,
            True,
            articles_processed=len(request.articles),
            successful_articles=successful_count,
            total_tokens=total_tokens,
            total_cost=total_cost
        )
        
        return BatchEnrichResponse(
            success=True,
            results=processed_results,
            total_articles=len(request.articles),
            successful_articles=successful_count,
            failed_articles=failed_count,
            total_processing_time_seconds=total_processing_time,
            total_tokens_used=total_tokens,
            total_cost_usd=total_cost
        )
        
    except Exception as e:
        total_processing_time = time.time() - start_time
        
        metrics.record_error("batch_enrichment_failed", "enrich_batch")
        logger.error("Batch enrichment failed", error=e)
        
        raise HTTPException(status_code=500, detail=str(e))

# Metrics endpoint
@app.get("/metrics")
async def get_metrics_summary():
    """Get service metrics summary"""
    try:
        summary = metrics.get_summary()
        return summary
    except Exception as e:
        logger.error("Failed to get metrics", error=e)
        raise HTTPException(status_code=500, detail=str(e))

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint with service information"""
    return {
        "service": "enrich_service",
        "version": "1.0.0",
        "description": "News Article Enrichment Service with LLM Summarization and Categorization",
        "endpoints": {
            "health": "/health",
            "enrich_single": "/enrich",
            "enrich_batch": "/enrich/batch",
            "metrics": "/metrics",
            "reading_metrics": "/reading-metrics/analyze",
            "config": "/config"
        },
        "timestamp": datetime.now(timezone.utc).isoformat()
    }

# Reading Metrics Endpoints
@app.post("/reading-metrics/analyze")
async def analyze_reading_metrics(article_data: dict):
    """Analyze reading metrics for a single article"""
    try:
        from reading_metrics import reading_metrics_calculator

        metrics = reading_metrics_calculator.calculate_all_metrics(article_data)

        return {
            'status': 'success',
            'article_uri': article_data.get('uri', 'unknown'),
            'metrics': metrics
        }

    except Exception as e:
        logger.error(f"Error analyzing reading metrics: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/reading-metrics/batch")
async def analyze_batch_reading_metrics(articles: List[dict]):
    """Analyze reading metrics for multiple articles"""
    try:
        from reading_metrics import reading_metrics_calculator

        results = []
        for article in articles:
            try:
                metrics = reading_metrics_calculator.calculate_all_metrics(article)
                results.append({
                    'article_uri': article.get('uri', 'unknown'),
                    'status': 'success',
                    'metrics': metrics
                })
            except Exception as e:
                results.append({
                    'article_uri': article.get('uri', 'unknown'),
                    'status': 'error',
                    'error': str(e)
                })

        successful = len([r for r in results if r['status'] == 'success'])

        return {
            'status': 'success',
            'total_articles': len(articles),
            'successful_analyses': successful,
            'failed_analyses': len(articles) - successful,
            'results': results
        }

    except Exception as e:
        logger.error(f"Error in batch reading metrics analysis: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/reading-metrics/stats")
async def get_reading_metrics_stats():
    """Get reading metrics statistics and configuration"""
    try:
        from reading_metrics import reading_metrics_calculator

        return {
            'status': 'success',
            'reading_speeds': reading_metrics_calculator.reading_speeds,
            'complexity_weights': reading_metrics_calculator.complexity_weights,
            'supported_metrics': [
                'reading_times',
                'word_metrics',
                'complexity_metrics',
                'difficulty_level',
                'engagement_metrics',
                'structure_metrics',
                'attention_metrics',
                'accessibility'
            ],
            'difficulty_levels': [
                'very_easy', 'easy', 'fairly_easy', 'standard',
                'fairly_difficult', 'difficult', 'very_difficult'
            ]
        }

    except Exception as e:
        logger.error(f"Error getting reading metrics stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "http_server:app",
        host="0.0.0.0",
        port=int(os.getenv("PORT", 8080)),
        log_level="info"
    )
