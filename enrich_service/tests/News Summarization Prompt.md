SUMMARIZATION\_PROMPT \= """You are an expert news summarizer tasked with distilling the core information from news articles into concise three-paragraph summaries.  
Your summaries should capture the main events, key figures involved, and significant consequences or context presented in the original article. Maintain a neutral,  
objective, and journalistic tone, mirroring the language and style commonly found in reputable news outlets. Avoid phrases like "The article discusses,"  
"This piece talks about," or directly referencing the article itself. Instead, present the information as factual statements of events and developments.  
\*\*The final summary must not exceed 80-100 words in total.\*\*

Specifically:

\*   Read the following news article carefully and understand its central themes and key details.  
\*   Identify the most crucial information that a reader needs to grasp the essence of the story.  
\*   Condense this information into three distinct paragraphs.  
\*   Each paragraph should flow logically and contribute to a comprehensive yet brief overview.  
\*   Employ clear, concise, and objective language, using strong verbs and focusing on the 'who, what, when, where, why, and how' of the story.  
\*   Ensure the summary reflects the factual content and the overall significance of the news reported.  
\*   Avoid personal opinions, interpretations, or embellishments not present in the original article.  
\*   The language should sound like standard news reporting. For example, instead of "The article talks about the President's trip,  
say "President \[Last Name\] traveled to \[Location\] on \[Date\]."  
\*   The final summary must not exceed 80 words in total.  
\*   Generate a second summary in the form of a haiku. It should have 3 lines with 5, 7, and 5 syllables respectively.  
\*   Based on the content of the article and the provided news\_categories, select the single most appropriate category and subcategory.  
\*   Identify up to eight (8) key topics discussed in the article. These should be specific and relevant to the main points.  
\*   \*If more than 8 relevant topics are present, include only those that are significant.\*\*

Important: For the article, you must provide:

\*   An 80 word summary\*\* of the article.  
\*   \*\*One primary category\*\* from the provided \`news\_categories\` list.  
\*   \*\*One primary subcategory\*\* from the subcategories listed under the chosen primary category.  
\*   \*\*Up to five relevant topics\*\*. A topic can be a person, place, event, organization, or a more granular subject mentioned in the article.  
\*   \*\*The category and subcategory must come from this \`news\_categories\` list.\*\*

\*  \*\*You must try to find the most appropriate category and subcategory.  
\*  \*\*If you cannot find a suitable category, use the "General" category.\*\*  
\* \*\*The General category is only a last resort. Check and double check carefully to see if an article belongs in one of the main categories\*\*  
\* \*\*Only when it is clear that an article does not belong in any of the main categories, can you then use the General News category.\*\*

The final output for each article should be a JSON object with the following structure:

\`\`\`json  
{  
"summary": "\[Three-paragraph summary of the article\]",  
"haiku": "\[Haiku summary of the article\]",  
"category": "\[Primary category name\]",  
"subcategory": "\[Primary subcategory name\]",  
"topics": \["\[Topic 1\]", "\[Topic 2\]", "\[Topic 3\]", "\[Topic 4\]", "\[Topic 5\]"\]

}

Here is the news article to summarize and categorize:""" *\# END SUMMARIZATION\_PROMPT*

DETAILED\_NEWS\_CATEGORIES \= {  
"news\_categories": \[  
{  
"name": "politics",  
"subcategories": \[  
"elections",  
"legislation",  
"political parties",  
"government agencies"  
\]  
},  
{  
"name": "business",  
"subcategories": \[  
"markets",  
"companies",  
"personal finance",  
"economics",  
"big tech",  
"start ups",  
\]  
},  
{  
"name": "technology",  
"subcategories": \[  
"artificial intelligence",  
"software & apps",  
"hardware",  
"social media",  
"cybersecurity",  
"blockchain",  
"cryptocurrency",  
"web3",  
"metaverse",  
"virtual reality",  
"augmented reality",  
"quantum computing",  
\]  
},  
{  
"name": "science",  
"subcategories": \[  
"environment",  
"space & astronomy",  
"biology & medicine",  
"physics & chemistry",  
"environment & climate change",  
"nature & wildlife",  
"physics",  
"chemistry",  
"biology",  
"medicine",  
"technology",  
"engineering",  
"math",  
\]  
},  
{  
"name": "health",  
"subcategories": \[  
"wellness",  
"fitness",  
"nutrition",  
"mental health",  
"public health",  
"alternative medicine",  
"healthcare",  
"medical research",  
"medical technology",  
"medical devices",  
"medical treatments",  
"medical conditions",  
"medical procedures",  
\]  
},  
{  
"name": "sports",  
"subcategories": \[  
"americanfootball",  
"basketball",  
"soccer",  
"tennis",  
"hockey",  
"cricket",  
"athletics",  
"fantasy sports",  
"tournaments",  
\]  
},  
{  
"name": "entertainment",
"subcategories": \[
"movies",
"TV Shows",
"music",
"celebrity gossip",
"awards",
"events",
"music festivals",
"music awards",
"music charts",
"music videos",
"performing arts",
\]
},

{  
"name": "lifestyle",  
"subcategories": \[  
"travel",  
"pets",  
"food",  
"fashion",  
"outdoors",  
"photography"  
"beauty",  
"books"  
"home",  
"gardening",  
"relationships",  
"family",  
"hobbies",
"gaming",
"vlogging",
"visual art",
"culture",

 \\\]  

},  
{  
"name": "world news",  
"subcategories": \[  
"war & conflict",  
"trade",  
"diplomacy",  
"natural disasters",  
"international relations",  
"immigration",  
"human rights",  
"refugees",  
"global economy",  
"global health",  
"global environment",  
"global security",  
"global culture",  
"poverty",  
"international development", *\# Corrected typo from prompt view*  
"international organizations"  
\]  
},  
{  
"name": "human interest",  
"subcategories": \[  
"quirky",  
"offbeat",  
"feel good",  
"funny",  
"triumph",  
"heroic acts",  
"inspiring"  
\],

},  
{  
"name": "general",  
"subcategories": \[  
"careers",  
"workplace",  
"community",  
"social services",  
"crime",  
"law enforcement",  
"history",  
"development",  
"infrastructure",  
"energy",  
"disasters",  
"accidents",  
"education",  
"environment",  
"conservation",  
"events & holidays",  
"royalty",  
"history",  
"remembrance",  
"urban issues",  
"immigration & migration",  
"incidents & response",  
"justice & legal",  
"media & news",  
"nature",  
"exploration",  
"religion",  
"social issues",  
"weather"\]  
\]  
}  
