"""
Enhanced Pydantic Models for News Summarization and Categorization

This module defines structured data models to ensure consistent LLM output validation
for the enhanced summarization prompt that includes summaries, haikus, categorization,
and topic extraction.
"""

from enum import Enum
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, validator
from datetime import datetime

class KeyTakeaway(BaseModel):
    """Individual key takeaway with explicit ordering."""
    index: int = Field(..., ge=1, le=3, description="Order index (1, 2, or 3)")
    text: str = Field(..., min_length=3, max_length=100, description="Takeaway text, max 8 words")

    @validator('text')
    @classmethod
    def validate_takeaway_text(cls, v):
        """Validate takeaway text format and word count."""
        word_count = len(v.split())
        if word_count > 12:  # Using 12 words for validation as requested
            raise ValueError(f"Takeaway too long: {word_count} words (max 12)")

        if word_count < 3:
            raise ValueError(f"Takeaway too short: {word_count} words (min 3)")

        if not v.strip().endswith(('.', '!', '?')):
            raise ValueError("Takeaway must end with complete sentences (., !, or ?)")

        return v

class NewsCategory(str, Enum):
    """Enum for news categories based on the detailed categories in the prompt."""
    POLITICS = "politics"
    BUSINESS = "business"
    TECHNOLOGY = "technology"
    SCIENCE = "science"
    HEALTH = "health"
    SPORTS = "sports"
    ENTERTAINMENT = "entertainment"
    LIFESTYLE = "lifestyle"
    WORLD_NEWS = "world news"
    HUMAN_INTEREST = "human interest"
    GENERAL = "general"
    # Additional categories that LLM might use
    CRIME = "crime"  # Maps to general
    LAW = "law"      # Maps to general

class PoliticsSubcategory(str, Enum):
    """Politics subcategories."""
    ELECTIONS = "elections"
    LEGISLATION = "legislation"
    POLITICAL_PARTIES = "political parties"
    GOVERNMENT_AGENCIES = "government agencies"

class BusinessSubcategory(str, Enum):
    """Business subcategories."""
    MARKETS = "markets"
    COMPANIES = "companies"
    PERSONAL_FINANCE = "personal finance"
    ECONOMICS = "economics"
    BIG_TECH = "big tech"
    START_UPS = "start ups"

class TechnologySubcategory(str, Enum):
    """Technology subcategories."""
    ARTIFICIAL_INTELLIGENCE = "artificial intelligence"
    SOFTWARE_APPS = "software & apps"
    HARDWARE = "hardware"
    SOCIAL_MEDIA = "social media"
    CYBERSECURITY = "cybersecurity"
    BLOCKCHAIN = "blockchain"
    CRYPTOCURRENCY = "cryptocurrency"
    WEB3 = "web3"
    METAVERSE = "metaverse"
    VIRTUAL_REALITY = "virtual reality"
    AUGMENTED_REALITY = "augmented reality"
    QUANTUM_COMPUTING = "quantum computing"

class ScienceSubcategory(str, Enum):
    """Science subcategories."""
    ENVIRONMENT = "environment"
    SPACE_ASTRONOMY = "space & astronomy"
    BIOLOGY_MEDICINE = "biology & medicine"
    PHYSICS_CHEMISTRY = "physics & chemistry"
    ENVIRONMENT_CLIMATE_CHANGE = "environment & climate change"
    NATURE_WILDLIFE = "nature & wildlife"
    PHYSICS = "physics"
    CHEMISTRY = "chemistry"
    BIOLOGY = "biology"
    MEDICINE = "medicine"
    TECHNOLOGY = "technology"
    ENGINEERING = "engineering"
    MATH = "math"

class HealthSubcategory(str, Enum):
    """Health subcategories."""
    WELLNESS = "wellness"
    FITNESS = "fitness"
    NUTRITION = "nutrition"
    MENTAL_HEALTH = "mental health"
    PUBLIC_HEALTH = "public health"
    ALTERNATIVE_MEDICINE = "alternative medicine"
    HEALTHCARE = "healthcare"
    MEDICAL_RESEARCH = "medical research"
    MEDICAL_TECHNOLOGY = "medical technology"
    MEDICAL_DEVICES = "medical devices"
    MEDICAL_TREATMENTS = "medical treatments"
    MEDICAL_CONDITIONS = "medical conditions"
    MEDICAL_PROCEDURES = "medical procedures"

class SportsSubcategory(str, Enum):
    """Sports subcategories."""
    AMERICAN_FOOTBALL = "americanfootball"
    BASKETBALL = "basketball"
    SOCCER = "soccer"
    TENNIS = "tennis"
    HOCKEY = "hockey"
    CRICKET = "cricket"
    ATHLETICS = "athletics"
    FANTASY_SPORTS = "fantasy sports"
    TOURNAMENTS = "tournaments"

class EntertainmentSubcategory(str, Enum):
    """Entertainment subcategories."""
    MOVIES = "movies"
    TV_SHOWS = "TV Shows"
    MUSIC = "music"
    CELEBRITY_GOSSIP = "celebrity gossip"
    AWARDS = "awards"
    EVENTS = "events"
    MUSIC_FESTIVALS = "music festivals"
    MUSIC_AWARDS = "music awards"
    MUSIC_CHARTS = "music charts"
    MUSIC_VIDEOS = "music videos"

class LifestyleSubcategory(str, Enum):
    """Lifestyle subcategories."""
    TRAVEL = "travel"
    PETS = "pets"
    FOOD = "food"
    FASHION = "fashion"
    OUTDOORS = "outdoors"
    PHOTOGRAPHY = "photography"
    BEAUTY = "beauty"
    BOOKS = "books"
    HOME = "home"
    GARDENING = "gardening"
    RELATIONSHIPS = "relationships"
    FAMILY = "family"
    HOBBIES = "hobbies"
    GAMING = "gaming"
    VLOGGING = "vlogging"
    ART = "art"
    CULTURE = "culture"

class WorldNewsSubcategory(str, Enum):
    """World News subcategories."""
    WAR_CONFLICT = "war & conflict"
    TRADE = "trade"
    DIPLOMACY = "diplomacy"
    NATURAL_DISASTERS = "natural disasters"
    INTERNATIONAL_RELATIONS = "international relations"
    IMMIGRATION = "immigration"
    HUMAN_RIGHTS = "human rights"
    REFUGEES = "refugees"
    GLOBAL_ECONOMY = "global economy"
    GLOBAL_HEALTH = "global health"
    GLOBAL_ENVIRONMENT = "global environment"
    GLOBAL_SECURITY = "global security"
    GLOBAL_CULTURE = "global culture"
    POVERTY = "poverty"
    INTERNATIONAL_DEVELOPMENT = "international development"
    INTERNATIONAL_ORGANIZATIONS = "international organizations"

class HumanInterestSubcategory(str, Enum):
    """Human Interest subcategories."""
    QUIRKY = "quirky"
    OFFBEAT = "offbeat"
    FEEL_GOOD = "feel good"
    FUNNY = "funny"
    TRIUMPH = "triumph"
    HEROIC_ACTS = "heroic acts"
    INSPIRING = "inspiring"

class GeneralSubcategory(str, Enum):
    """General subcategories."""
    CAREERS = "careers"
    WORKPLACE = "workplace"
    COMMUNITY = "community"
    SOCIAL_SERVICES = "social services"
    CRIME = "crime"
    LAW_ENFORCEMENT = "law enforcement"
    HISTORY = "history"
    DEVELOPMENT = "development"
    INFRASTRUCTURE = "infrastructure"
    ENERGY = "energy"
    DISASTERS = "disasters"
    ACCIDENTS = "accidents"
    EDUCATION = "education"
    ENVIRONMENT = "environment"
    CONSERVATION = "conservation"
    EVENTS_HOLIDAYS = "events & holidays"
    ROYALTY = "royalty"
    REMEMBRANCE = "remembrance"
    URBAN_ISSUES = "urban issues"
    IMMIGRATION_MIGRATION = "immigration & migration"
    INCIDENTS_RESPONSE = "incidents & response"
    JUSTICE_LEGAL = "justice & legal"
    MEDIA_NEWS = "media & news"
    NATURE = "nature"
    EXPLORATION = "exploration"
    RELIGION = "religion"
    SOCIAL_ISSUES = "social issues"
    WEATHER = "weather"

class EnhancedSummarizationResponse(BaseModel):
    """Pydantic model for enhanced summarization response with validation."""
    summary: str = Field(
        ...,
        min_length=50,
        max_length=800,  # Increased to accommodate longer summaries
        description="100-word summary of the article"
    )
    haiku: str = Field(
        ...,
        min_length=10,
        max_length=100,
        description="Haiku summary with 5-7-5 syllable structure"
    )
    key_takeaways: List[KeyTakeaway] = Field(
        ...,
        min_length=3,
        max_length=3,
        description="Three key takeaway sentences with explicit ordering"
    )
    category: NewsCategory = Field(
        ...,
        description="Primary category from the news_categories list"
    )
    subcategory: str = Field(
        ...,
        min_length=2,
        max_length=50,
        description="Primary subcategory under the chosen category"
    )

    @validator('summary')
    def validate_summary_word_count(cls, v):
        word_count = len(v.split())
        if word_count > 100:
            raise ValueError(f"Summary exceeds 100 words (got {word_count})")
        return v

    @validator('key_takeaways')
    def validate_key_takeaways(cls, v):
        """Validate key takeaways structure and ordering."""
        if not isinstance(v, list):
            raise ValueError("key_takeaways must be a list")

        if len(v) != 3:
            raise ValueError(f"key_takeaways must contain exactly 3 items (got {len(v)})")

        # Check that we have exactly indices 1, 2, 3
        indices = [takeaway.index for takeaway in v]
        expected_indices = [1, 2, 3]

        if sorted(indices) != expected_indices:
            raise ValueError(f"key_takeaways must have indices [1, 2, 3], got {sorted(indices)}")

        # Check for duplicate indices
        if len(set(indices)) != len(indices):
            raise ValueError("key_takeaways cannot have duplicate indices")

        return v
    
    class Config:
        use_enum_values = True

class OriginalArticleData(BaseModel):
    """Model for original article data from GCS."""
    uri: str = Field(..., description="Article unique identifier")
    title: str = Field(..., description="Article title")
    body: str = Field(..., description="Article body content")
    source_title: Optional[str] = Field(None, description="Source publication name")
    original_categories: List[str] = Field(default=[], description="Original categories from source")
    concepts: List[Dict[str, Any]] = Field(default=[], description="Original concepts/topics from EventRegistry")
    concept_labels: List[str] = Field(default=[], description="Extracted concept labels in English")
    source_file: Optional[str] = Field(None, description="GCS source file path")

class EnhancedArticleResult(BaseModel):
    """Complete result combining original data with LLM enhancements."""
    # Original data
    uri: str
    title: str
    original_category: Optional[str] = None
    original_concept_labels: List[str] = Field(default=[], description="Original concept labels in English")
    source_title: Optional[str] = None

    # LLM generated fields
    summary: str
    haiku: str
    llm_category: str
    llm_subcategory: str

    # Metadata
    processing_time: Optional[float] = None
    timestamp: datetime = Field(default_factory=datetime.now)
    error: Optional[str] = None

    class Config:
        use_enum_values = True
