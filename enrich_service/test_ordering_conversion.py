#!/usr/bin/env python3
"""
Test script to verify that key_takeaways ordering is preserved during conversion
from structured KeyTakeaway objects to simple list format for Firestore storage.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models import KeyTakeaway

def test_ordering_conversion():
    """Test that KeyTakeaway objects are correctly sorted and converted to ordered list"""
    print("Testing key_takeaways ordering conversion...")
    
    # Create key takeaways in random order (simulating LLM response)
    key_takeaways = [
        KeyTakeaway(index=3, text="Third takeaway here."),
        KeyTakeaway(index=1, text="First takeaway here."),
        KeyTakeaway(index=2, text="Second takeaway here.")
    ]
    
    print(f"Original order: {[kt.index for kt in key_takeaways]}")
    
    # Sort by index to ensure proper ordering (this is what HTTP server does)
    sorted_takeaways = sorted(key_takeaways, key=lambda x: x.index)
    
    # Convert to simple list (this is what gets stored in Firestore)
    firestore_format = [takeaway.text for takeaway in sorted_takeaways]
    
    print(f"Sorted order: {[kt.index for kt in sorted_takeaways]}")
    print(f"Firestore format: {firestore_format}")
    
    # Verify correct ordering
    expected_order = [
        "First takeaway here.",
        "Second takeaway here.", 
        "Third takeaway here."
    ]
    
    if firestore_format == expected_order:
        print("✅ Ordering conversion works correctly!")
        return True
    else:
        print(f"❌ Ordering conversion failed!")
        print(f"Expected: {expected_order}")
        print(f"Got: {firestore_format}")
        return False

def test_index_validation():
    """Test that indices must be 1, 2, 3"""
    print("\nTesting index validation...")
    
    try:
        # Test invalid index (0)
        KeyTakeaway(index=0, text="Invalid index zero.")
        print("❌ Should have failed for index 0")
        return False
    except Exception as e:
        print(f"✅ Correctly rejected index 0: {e}")
    
    try:
        # Test invalid index (4)
        KeyTakeaway(index=4, text="Invalid index four.")
        print("❌ Should have failed for index 4")
        return False
    except Exception as e:
        print(f"✅ Correctly rejected index 4: {e}")
    
    try:
        # Test valid indices
        for i in [1, 2, 3]:
            KeyTakeaway(index=i, text=f"Valid index {i}.")
        print("✅ All valid indices (1, 2, 3) accepted")
        return True
    except Exception as e:
        print(f"❌ Valid indices failed: {e}")
        return False

def main():
    """Run all tests"""
    print("Testing key_takeaways ordering and validation...\n")
    
    tests = [
        test_ordering_conversion,
        test_index_validation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n{'='*50}")
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All ordering tests passed!")
        print("\n📋 Summary:")
        print("• KeyTakeaway objects maintain explicit ordering with index field")
        print("• HTTP server sorts by index before converting to Firestore format")
        print("• Firestore receives ordered list: [text1, text2, text3]")
        print("• Order is guaranteed regardless of LLM response order")
        return True
    else:
        print("❌ Some ordering tests failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
