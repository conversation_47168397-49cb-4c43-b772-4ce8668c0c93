#!/usr/bin/env python3
"""
Test script to verify that topic_keywords brings in the top 7 concepts.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_topic_keywords_creation():
    """Test that topic_keywords are created with top 7 concepts"""
    print("Testing topic_keywords creation with 7 concepts...")
    
    # Mock article with 10 concept labels
    test_article = {
        'uri': 'test-article-789',
        'title': 'Test Article for Topic Keywords',
        'body': 'Test article content',
        'concept_labels': [
            'technology',      # 1
            'innovation',      # 2  
            'artificial intelligence',  # 3
            'machine learning',         # 4
            'business',                 # 5
            'market growth',           # 6
            'investment',              # 7
            'startup',                 # 8 - should be excluded
            'venture capital',         # 9 - should be excluded
            'entrepreneurship'         # 10 - should be excluded
        ]
    }
    
    # Simulate the topic_keywords creation logic from http_server.py
    if not test_article.get('topic_keywords'):
        test_article['topic_keywords'] = [
            {
                'keyword': topic,
                'trending_score': 0.5,
                'relevance_score': 0.8
            } for topic in test_article.get('concept_labels', [])[:7]  # Top 7
        ]
    
    # Extract custom_topic_names from topic_keywords
    topic_keywords = test_article.get('topic_keywords', [])
    test_article['custom_topic_names'] = [
        kw.get('keyword', '') for kw in topic_keywords
        if isinstance(kw, dict) and kw.get('keyword')
    ][:7]  # Limit to top 7 topics
    
    # Verify results
    topic_keywords_count = len(test_article['topic_keywords'])
    custom_topic_names_count = len(test_article['custom_topic_names'])
    
    print(f"📊 Results:")
    print(f"   - Original concept_labels count: {len(test_article['concept_labels'])}")
    print(f"   - topic_keywords count: {topic_keywords_count}")
    print(f"   - custom_topic_names count: {custom_topic_names_count}")
    
    print(f"\n📋 topic_keywords content:")
    for i, kw in enumerate(test_article['topic_keywords'], 1):
        print(f"   {i}. {kw['keyword']}")
    
    print(f"\n📋 custom_topic_names content:")
    for i, name in enumerate(test_article['custom_topic_names'], 1):
        print(f"   {i}. {name}")
    
    # Verify we have exactly 7 topics
    if topic_keywords_count == 7 and custom_topic_names_count == 7:
        print("\n✅ SUCCESS: Both topic_keywords and custom_topic_names have exactly 7 items!")
        
        # Verify the excluded concepts
        excluded_concepts = test_article['concept_labels'][7:]
        print(f"\n📝 Excluded concepts (as expected): {excluded_concepts}")
        
        return True
    else:
        print(f"\n❌ FAILURE: Expected 7 items each, got {topic_keywords_count} topic_keywords and {custom_topic_names_count} custom_topic_names")
        return False

def test_fallback_scenarios():
    """Test fallback scenarios also use 7 concepts"""
    print("\nTesting fallback scenarios...")
    
    # Test article with 10 concept labels
    test_article = {
        'uri': 'test-fallback-123',
        'concept_labels': [
            'politics', 'election', 'government', 'policy', 'legislation',
            'congress', 'senate', 'democracy', 'voting', 'campaign'
        ]
    }
    
    # Simulate fallback logic from http_server.py (when LLM fails)
    test_article['custom_category_name'] = 'general'
    test_article['custom_subcategory_name'] = 'general'
    test_article['custom_topic_names'] = test_article.get('concept_labels', [])[:7]  # Top 7
    test_article['topic_keywords'] = []
    
    fallback_count = len(test_article['custom_topic_names'])
    
    print(f"📊 Fallback Results:")
    print(f"   - Original concept_labels count: {len(test_article['concept_labels'])}")
    print(f"   - Fallback custom_topic_names count: {fallback_count}")
    
    print(f"\n📋 Fallback custom_topic_names:")
    for i, name in enumerate(test_article['custom_topic_names'], 1):
        print(f"   {i}. {name}")
    
    if fallback_count == 7:
        print("\n✅ SUCCESS: Fallback scenario also uses exactly 7 concepts!")
        return True
    else:
        print(f"\n❌ FAILURE: Expected 7 fallback concepts, got {fallback_count}")
        return False

def main():
    """Run all topic keywords tests"""
    print("Testing topic_keywords implementation for 7 concepts...\n")
    
    tests = [
        test_topic_keywords_creation,
        test_fallback_scenarios
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n{'='*60}")
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All topic keywords tests passed!")
        print("\n📋 Summary:")
        print("• topic_keywords now includes top 7 concepts (was 5)")
        print("• custom_topic_names extracts up to 7 topics from topic_keywords")
        print("• Fallback scenarios also use 7 concepts")
        print("• Schema documentation updated to reflect 7 topics")
        return True
    else:
        print("❌ Some topic keywords tests failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
